#!/usr/bin/env python3
"""
TS to MP4 Converter

This script recursively finds all .ts files in a specified directory
and converts them to .mp4 files using ffmpeg.
"""

import os
import sys
import argparse
import subprocess
import shutil
from pathlib import Path
import time


def check_ffmpeg():
    """
    检查系统是否安装了ffmpeg
    
    Returns:
        bool: True if ffmpeg is available, False otherwise
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = file_path.stat().st_size
        return size_bytes / (1024 * 1024)
    except:
        return 0


def check_file_with_ffprobe(file_path):
    """
    使用ffprobe检查文件信息

    Args:
        file_path (Path): 文件路径

    Returns:
        tuple: (is_valid, info_dict)
    """
    try:
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', str(file_path)
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode == 0:
            import json
            info = json.loads(result.stdout)
            return True, info
        else:
            return False, {"error": result.stderr}
    except Exception as e:
        return False, {"error": str(e)}


def convert_ts_to_mp4(directory_path, dry_run=False, quality='medium', overwrite=False, 
                     delete_source=False, max_workers=1):
    """
    Recursively convert all .ts files to .mp4 files in the specified directory using ffmpeg.
    
    Args:
        directory_path (str): The directory path to search
        dry_run (bool): If True, only show what would be converted without actually converting
        quality (str): Conversion quality - 'high', 'medium', 'low', 'copy'
        overwrite (bool): If True, overwrite existing .mp4 files
        delete_source (bool): If True, delete source .ts files after successful conversion
        max_workers (int): Maximum number of parallel conversions (currently not implemented)
    
    Returns:
        tuple: (success_count, error_count, errors)
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"错误: 目录 '{directory_path}' 不存在")
        return 0, 1, [f"目录 '{directory_path}' 不存在"]
    
    if not directory.is_dir():
        print(f"错误: '{directory_path}' 不是一个目录")
        return 0, 1, [f"'{directory_path}' 不是一个目录"]
    
    # 检查ffmpeg是否可用
    if not check_ffmpeg():
        error_msg = "错误: 系统中未找到ffmpeg，请先安装ffmpeg"
        print(error_msg)
        print("安装方法:")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  Windows: 从 https://ffmpeg.org/download.html 下载")
        return 0, 1, [error_msg]
    
    # 递归查找所有.ts文件
    ts_files = list(directory.rglob("*.ts"))
    
    if not ts_files:
        print(f"在目录 '{directory_path}' 中没有找到任何 .ts 文件")
        return 0, 0, []
    
    print(f"找到 {len(ts_files)} 个 .ts 文件")
    
    # 设置ffmpeg质量参数
    quality_settings = {
        'copy': ['-c', 'copy'],  # 直接复制流，最快但可能有兼容性问题
        'high': ['-c:v', 'libx264', '-crf', '18', '-c:a', 'aac', '-b:a', '192k'],
        'medium': ['-c:v', 'libx264', '-crf', '23', '-c:a', 'aac', '-b:a', '128k'],
        'low': ['-c:v', 'libx264', '-crf', '28', '-c:a', 'aac', '-b:a', '96k']
    }
    
    ffmpeg_params = quality_settings.get(quality, quality_settings['medium'])
    
    success_count = 0
    error_count = 0
    errors = []
    total_size_mb = 0
    converted_size_mb = 0
    
    # 计算总文件大小
    for ts_file in ts_files:
        total_size_mb += get_file_size_mb(ts_file)
    
    print(f"总文件大小: {total_size_mb:.1f} MB")
    print(f"转换质量: {quality}")
    print("-" * 60)
    
    for i, ts_file in enumerate(ts_files, 1):
        file_size_mb = get_file_size_mb(ts_file)
        
        # 构造新的.mp4文件名
        mp4_file = ts_file.with_suffix('.mp4')
        
        print(f"[{i}/{len(ts_files)}] 处理: {ts_file.name} ({file_size_mb:.1f} MB)")

        if dry_run:
            print(f"  [预览] {ts_file} -> {mp4_file}")
            success_count += 1
            converted_size_mb += file_size_mb
        else:
            # 首先检查文件是否有效
            is_valid, file_info = check_file_with_ffprobe(ts_file)
            if not is_valid:
                error_msg = f"文件检查失败 {ts_file}: {file_info.get('error', '未知错误')}"
                print(f"  ❌ {error_msg}")
                errors.append(error_msg)
                error_count += 1
                continue
            try:
                # 检查目标文件是否已存在
                if mp4_file.exists() and not overwrite:
                    error_msg = f"目标文件已存在: {mp4_file}"
                    print(f"  跳过: {error_msg}")
                    errors.append(error_msg)
                    error_count += 1
                    continue
                
                print(f"  正在转换: {ts_file.name} -> {mp4_file.name}")
                
                # 记录开始时间
                start_time = time.time()
                
                # 构建ffmpeg命令
                cmd = ['ffmpeg', '-i', str(ts_file)]

                # 添加输入选项来处理可能的问题
                cmd.extend(['-fflags', '+genpts'])  # 生成时间戳
                cmd.extend(['-avoid_negative_ts', 'make_zero'])  # 避免负时间戳

                cmd.extend(ffmpeg_params)

                # 添加覆盖选项
                if overwrite:
                    cmd.append('-y')
                else:
                    cmd.append('-n')

                cmd.append(str(mp4_file))
                
                # 执行ffmpeg转换
                result = subprocess.run(cmd,
                                      capture_output=True,
                                      text=True,
                                      timeout=600)  # 10分钟超时
                
                # 计算转换时间
                elapsed_time = time.time() - start_time
                
                if result.returncode == 0:
                    output_size_mb = get_file_size_mb(mp4_file)
                    compression_ratio = (file_size_mb - output_size_mb) / file_size_mb * 100 if file_size_mb > 0 else 0
                    
                    print(f"  ✅ 转换成功: {elapsed_time:.1f}s, "
                          f"输出: {output_size_mb:.1f} MB, "
                          f"压缩率: {compression_ratio:.1f}%")
                    
                    success_count += 1
                    converted_size_mb += file_size_mb
                    
                    # 如果设置了删除源文件选项
                    if delete_source:
                        try:
                            ts_file.unlink()
                            print(f"  🗑️  已删除源文件: {ts_file.name}")
                        except Exception as e:
                            print(f"  ⚠️  删除源文件失败: {e}")
                else:
                    # 分析具体的错误原因
                    error_details = []
                    if result.returncode == 1:
                        error_details.append("一般错误")
                    elif result.returncode == 8:
                        error_details.append("输入文件格式错误或损坏")
                    elif result.returncode == 69:
                        error_details.append("输出文件无法创建")
                    else:
                        error_details.append(f"未知错误码: {result.returncode}")

                    # 分析stderr中的关键错误信息
                    stderr_lower = result.stderr.lower() if result.stderr else ""
                    if "no such file" in stderr_lower:
                        error_details.append("文件不存在")
                    elif "permission denied" in stderr_lower:
                        error_details.append("权限不足")
                    elif "invalid data" in stderr_lower or "corrupt" in stderr_lower:
                        error_details.append("文件数据损坏")
                    elif "unsupported" in stderr_lower:
                        error_details.append("不支持的格式")
                    elif "no space left" in stderr_lower:
                        error_details.append("磁盘空间不足")

                    error_msg = f"转换失败 {ts_file.name}: {', '.join(error_details)}"
                    if result.stderr:
                        # 提取关键错误行
                        stderr_lines = result.stderr.split('\n')
                        error_lines = [line for line in stderr_lines if 'error' in line.lower() or 'failed' in line.lower()]
                        if error_lines:
                            error_msg += f"\n关键错误: {error_lines[-1][:200]}"
                        else:
                            error_msg += f"\n详细信息: {result.stderr[-300:]}"

                    print(f"  ❌ {error_msg}")
                    errors.append(error_msg)
                    error_count += 1
                
            except subprocess.TimeoutExpired:
                error_msg = f"转换超时 {ts_file}: 转换时间超过10分钟"
                print(f"  ❌ {error_msg}")
                errors.append(error_msg)
                error_count += 1
            except Exception as e:
                error_msg = f"转换失败 {ts_file}: {str(e)}"
                print(f"  ❌ {error_msg}")
                errors.append(error_msg)
                error_count += 1
        
        # 显示进度
        progress = (converted_size_mb / total_size_mb * 100) if total_size_mb > 0 else 0
        print(f"  进度: {progress:.1f}% ({converted_size_mb:.1f}/{total_size_mb:.1f} MB)")
        print()
    
    return success_count, error_count, errors


def main():
    parser = argparse.ArgumentParser(
        description="递归转换目录下所有.ts文件为.mp4文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python convert_ts_to_mp4.py /path/to/videos
  python convert_ts_to_mp4.py /path/to/videos --dry-run
  python convert_ts_to_mp4.py /path/to/videos --quality high
  python convert_ts_to_mp4.py /path/to/videos --overwrite --delete-source
  python convert_ts_to_mp4.py .

质量选项说明:
  copy   - 直接复制流，最快但可能有兼容性问题
  high   - 高质量 (CRF 18, 192k音频)
  medium - 中等质量 (CRF 23, 128k音频) [默认]
  low    - 低质量 (CRF 28, 96k音频)
        """
    )
    
    parser.add_argument(
        "directory",
        help="要处理的目录路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，只显示将要转换的文件，不实际执行转换操作"
    )
    
    parser.add_argument(
        "--quality", "-q",
        choices=['copy', 'high', 'medium', 'low'],
        default='medium',
        help="转换质量 (默认: medium)"
    )
    
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="覆盖已存在的.mp4文件"
    )
    
    parser.add_argument(
        "--delete-source",
        action="store_true",
        help="转换成功后删除源.ts文件 (谨慎使用!)"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    args = parser.parse_args()
    
    # 转换为绝对路径
    directory_path = os.path.abspath(args.directory)
    
    print("=" * 60)
    print("TS to MP4 转换器")
    print("=" * 60)
    print(f"处理目录: {directory_path}")
    print(f"转换质量: {args.quality}")
    
    if args.dry_run:
        print("运行模式: 预览模式 (不会实际转换文件)")
    else:
        print("运行模式: 实际转换")
        if args.overwrite:
            print("覆盖模式: 启用")
        if args.delete_source:
            print("⚠️  警告: 将在转换成功后删除源文件!")
            response = input("确认继续? (y/N): ")
            if response.lower() != 'y':
                print("操作已取消")
                sys.exit(0)
    
    print("=" * 60)
    
    # 执行转换操作
    start_time = time.time()
    success_count, error_count, errors = convert_ts_to_mp4(
        directory_path, 
        dry_run=args.dry_run,
        quality=args.quality,
        overwrite=args.overwrite,
        delete_source=args.delete_source
    )
    total_time = time.time() - start_time
    
    print("=" * 60)
    print(f"处理完成 (耗时: {total_time:.1f}秒):")
    print(f"  ✅ 成功: {success_count} 个文件")
    print(f"  ❌ 失败: {error_count} 个文件")
    
    if errors and args.verbose:
        print("\n错误详情:")
        for error in errors:
            print(f"  - {error}")
    
    # 返回适当的退出码
    sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
