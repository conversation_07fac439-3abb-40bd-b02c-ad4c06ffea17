#!/usr/bin/env python3
"""
测试断点续传功能
"""

import sys
import csv
import tempfile
from pathlib import Path
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from upload_mp4_to_oss import (
    load_upload_history, 
    save_upload_record, 
    is_file_uploaded,
    calculate_file_hash
)

def create_test_csv_with_records():
    """创建测试CSV文件，包含一些上传记录"""
    
    # 创建临时目录和文件
    temp_dir = Path(tempfile.mkdtemp())
    csv_file = temp_dir / "test_uploads.csv"
    
    # 创建一些测试文件
    test_files = []
    for i in range(3):
        test_file = temp_dir / f"test_video_{i}.mp4"
        test_file.write_text(f"Test video content {i}")
        test_files.append(test_file)
    
    print(f"📁 创建测试目录: {temp_dir}")
    print(f"📄 创建测试CSV: {csv_file}")
    
    # 创建测试记录
    test_records = [
        {
            'uuid': 'test-uuid-1',
            'filename': 'test_video_0.mp4',
            'oss_key': 'video/test/test_video_0.mp4',
            'file_size_mb': '0.01',
            'file_hash': calculate_file_hash(test_files[0]),
            'file_path': str(test_files[0]),
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'completed'
        },
        {
            'uuid': 'test-uuid-2',
            'filename': 'test_video_1.mp4',
            'oss_key': 'video/test/test_video_1.mp4',
            'file_size_mb': '0.01',
            'file_hash': calculate_file_hash(test_files[1]),
            'file_path': str(test_files[1]),
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'failed'
        }
    ]
    
    # 保存记录到CSV
    for record in test_records:
        save_upload_record(csv_file, record)
    
    return temp_dir, csv_file, test_files

def test_load_upload_history():
    """测试加载上传历史功能"""
    print("\n🧪 测试加载上传历史")
    print("-" * 50)
    
    temp_dir, csv_file, test_files = create_test_csv_with_records()
    
    # 加载历史记录
    history = load_upload_history(csv_file)
    
    print(f"✅ 加载了 {len(history)} 条历史记录")
    
    for file_path, record in history.items():
        print(f"📄 {Path(file_path).name}")
        print(f"   状态: {record['status']}")
        print(f"   哈希: {record['file_hash'][:8]}...")
        print(f"   时间: {record['upload_time']}")
    
    return temp_dir, csv_file, test_files, history

def test_is_file_uploaded():
    """测试文件上传状态检查"""
    print("\n🧪 测试文件上传状态检查")
    print("-" * 50)
    
    temp_dir, csv_file, test_files, history = test_load_upload_history()
    
    for i, test_file in enumerate(test_files):
        is_uploaded, record = is_file_uploaded(test_file, history)
        
        print(f"📄 {test_file.name}")
        print(f"   是否已上传: {'✅ 是' if is_uploaded else '❌ 否'}")
        
        if record:
            print(f"   记录状态: {record.get('status', '未知')}")
            print(f"   记录时间: {record.get('upload_time', '未知')}")
        else:
            print(f"   记录状态: 无记录")
        print()
    
    return temp_dir

def test_file_change_detection():
    """测试文件更改检测"""
    print("\n🧪 测试文件更改检测")
    print("-" * 50)
    
    temp_dir, csv_file, test_files = create_test_csv_with_records()
    
    # 加载历史记录
    history = load_upload_history(csv_file)
    
    # 修改第一个文件
    test_files[0].write_text("Modified content")
    print(f"📝 修改文件: {test_files[0].name}")
    
    # 检查修改后的文件
    is_uploaded, record = is_file_uploaded(test_files[0], history)
    
    print(f"📄 {test_files[0].name}")
    print(f"   是否已上传: {'✅ 是' if is_uploaded else '❌ 否'}")
    print(f"   原因: {'文件内容已更改' if not is_uploaded and record else '其他'}")
    
    return temp_dir

def test_save_upload_record():
    """测试保存上传记录"""
    print("\n🧪 测试保存上传记录")
    print("-" * 50)
    
    temp_dir = Path(tempfile.mkdtemp())
    csv_file = temp_dir / "new_uploads.csv"
    
    # 创建测试记录
    test_record = {
        'uuid': 'new-test-uuid',
        'filename': 'new_video.mp4',
        'oss_key': 'video/test/new_video.mp4',
        'file_size_mb': '1.23',
        'file_hash': 'abcd1234567890',
        'file_path': '/test/path/new_video.mp4',
        'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'status': 'completed'
    }
    
    # 保存记录
    save_upload_record(csv_file, test_record)
    
    print(f"✅ 保存记录到: {csv_file}")
    
    # 验证保存的内容
    with open(csv_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(f"📄 文件内容:")
        print(content)
    
    return temp_dir

def cleanup_test_dirs(*dirs):
    """清理测试目录"""
    print("\n🧹 清理测试文件")
    print("-" * 50)
    
    import shutil
    for temp_dir in dirs:
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"🗑️  删除: {temp_dir}")

def main():
    """运行所有测试"""
    print("🧪 断点续传功能测试")
    print("=" * 80)
    
    test_dirs = []
    
    try:
        # 运行各项测试
        temp_dir1 = test_load_upload_history()[0]
        test_dirs.append(temp_dir1)
        
        temp_dir2 = test_file_change_detection()
        test_dirs.append(temp_dir2)
        
        temp_dir3 = test_save_upload_record()
        test_dirs.append(temp_dir3)
        
        print("\n" + "=" * 80)
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        cleanup_test_dirs(*test_dirs)

if __name__ == "__main__":
    main()
