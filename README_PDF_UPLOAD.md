# PDF文件上传到阿里云OSS工具

## 🚀 快速开始

### 1. 环境配置

```bash
# 运行配置脚本
./setup_pdf_upload.sh

# 或手动安装依赖
pip install oss2
```

### 2. 设置环境变量

```bash
export OSS_ACCESS_KEY_ID="your_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_access_key_secret"
export OSS_ENDPOINT="oss-cn-beijing.aliyuncs.com"
export OSS_BUCKET_NAME="your_bucket_name"
```

### 3. 运行工具

```bash
# 基本用法
python upload_pdf_to_oss.py /path/to/pdf/directory

# 指定输出CSV文件
python upload_pdf_to_oss.py /path/to/pdf/directory --csv my_pdf_files.csv
```

## 📁 文件结构

```
pdf-upload-tool/
├── upload_pdf_to_oss.py      # 主程序
├── test_pdf_upload.py        # 测试脚本
├── setup_pdf_upload.sh       # 环境配置脚本
├── pdf_upload_example.md     # 详细使用说明
└── README_PDF_UPLOAD.md      # 快速指南（本文件）
```

## 🎯 功能特性

- ✅ **递归扫描** - 自动扫描目录下所有PDF文件
- ✅ **智能路径** - 根据目录结构生成OSS存储路径
- ✅ **UUID生成** - 为每个文件生成唯一标识符
- ✅ **CSV记录** - 详细记录上传信息
- ✅ **私有访问** - 文件设置为私有访问控制
- ✅ **完整性验证** - MD5哈希值验证
- ✅ **错误处理** - 完善的错误处理机制

## 📊 OSS Key生成规则

根据"视频所在目录往上拼接2级，最前边拼接'pdf'"的规则：

| 原始路径 | OSS Key |
|---------|---------|
| `/books/数学/七年级/上册/第一章.pdf` | `pdf/七年级/上册/第一章.pdf` |
| `/books/语文/八年级/下册/课文.pdf` | `pdf/八年级/下册/课文.pdf` |

## 📄 CSV输出格式

生成的CSV文件包含以下字段：

- `uuid` - 文件唯一标识符
- `filename` - PDF文件名
- `oss_key` - OSS存储路径
- `file_size_mb` - 文件大小(MB)
- `file_hash` - 文件MD5哈希值
- `file_path` - 本地文件完整路径
- `upload_time` - 上传时间
- `status` - 上传状态

## 🧪 测试工具

```bash
# 运行完整测试套件
python test_pdf_upload.py

# 测试包含：
# - 环境配置检查
# - OSS Key生成逻辑
# - 文件扫描功能
# - CSV生成功能
```

## ⚠️ 注意事项

1. **环境变量** - 确保所有OSS相关环境变量已正确设置
2. **网络连接** - 需要稳定的网络连接到阿里云OSS
3. **权限设置** - OSS访问密钥需要有上传权限
4. **文件大小** - 大文件上传可能需要较长时间
5. **存储空间** - 确保OSS存储空间充足

## 🔧 故障排除

### 常见问题

1. **OSS连接失败**
   ```bash
   # 检查环境变量
   echo $OSS_ACCESS_KEY_ID
   echo $OSS_ENDPOINT
   ```

2. **权限错误**
   - 确认OSS访问密钥有上传权限
   - 检查Bucket策略设置

3. **文件上传失败**
   - 检查网络连接
   - 确认文件路径正确
   - 验证文件未被占用

### 调试模式

```bash
# 启用详细日志
export OSS_DEBUG=1
python upload_pdf_to_oss.py /path/to/pdf/directory
```

## 📞 支持

如果遇到问题，请：

1. 首先运行测试脚本：`python test_pdf_upload.py`
2. 检查环境变量配置
3. 查看详细使用说明：`pdf_upload_example.md`
4. 检查错误日志和状态信息

## 🔄 版本历史

- **v1.0** - 初始版本，支持PDF文件批量上传到OSS
- 支持递归目录扫描
- 智能OSS Key生成
- CSV记录功能
- 完整的错误处理

---

**开发者**: Augment Agent  
**最后更新**: 2024-01-15
