#!/usr/bin/env python3
"""
PDF压缩工具

功能：
1. 压缩PDF文件到原来的20%左右大小
2. 尽量保证PDF质量
3. 支持多种压缩策略
4. 批量处理功能

使用方法：
python pdf_compressor.py input.pdf output.pdf
python pdf_compressor.py --batch /path/to/pdf/directory

依赖安装：
pip install PyPDF2 reportlab Pillow
"""

import os
import sys
import argparse
from pathlib import Path
from typing import List, Tuple, Optional
import tempfile
import shutil

try:
    import PyPDF2
    from PyPDF2 import PdfReader, PdfWriter
except ImportError:
    print("❌ 请安装PyPDF2: pip install PyPDF2")
    sys.exit(1)

try:
    from PIL import Image
except ImportError:
    print("❌ 请安装Pillow: pip install Pillow")
    sys.exit(1)

try:
    from reportlab.pdfgen import canvas
    from reportlab.lib.pagesizes import letter
    from reportlab.lib.utils import ImageReader
except ImportError:
    print("❌ 请安装reportlab: pip install reportlab")
    sys.exit(1)

# 尝试导入Ghostscript支持（可选）
try:
    import subprocess
    GHOSTSCRIPT_AVAILABLE = True
except ImportError:
    GHOSTSCRIPT_AVAILABLE = False

class PDFCompressor:
    def __init__(self, target_compression_ratio: float = 0.2):
        """
        初始化PDF压缩器
        
        Args:
            target_compression_ratio: 目标压缩比例（默认0.2，即20%）
        """
        self.target_ratio = target_compression_ratio
        self.temp_dir = None
    
    def __enter__(self):
        """上下文管理器入口"""
        self.temp_dir = tempfile.mkdtemp(prefix="pdf_compress_")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口，清理临时文件"""
        if self.temp_dir and Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
    
    def get_file_size_mb(self, file_path: Path) -> float:
        """获取文件大小（MB）"""
        return file_path.stat().st_size / (1024 * 1024)
    
    def compress_images_in_pdf(self, input_path: Path, output_path: Path, 
                              image_quality: int = 60, max_width: int = 1200) -> bool:
        """
        压缩PDF中的图像
        
        Args:
            input_path: 输入PDF路径
            output_path: 输出PDF路径
            image_quality: 图像质量(1-100)
            max_width: 图像最大宽度
        """
        try:
            reader = PdfReader(str(input_path))
            writer = PdfWriter()
            
            print(f"   📄 处理 {len(reader.pages)} 页PDF...")
            
            for page_num, page in enumerate(reader.pages):
                # 复制页面
                writer.add_page(page)
                
                # 处理页面中的图像
                if '/XObject' in page['/Resources']:
                    xObject = page['/Resources']['/XObject'].get_object()
                    
                    for obj in xObject:
                        if xObject[obj]['/Subtype'] == '/Image':
                            try:
                                # 提取图像数据
                                img_obj = xObject[obj]
                                img_data = img_obj.get_data()
                                
                                # 使用PIL处理图像
                                temp_img_path = Path(self.temp_dir) / f"temp_img_{page_num}_{obj}.jpg"
                                
                                with open(temp_img_path, 'wb') as f:
                                    f.write(img_data)
                                
                                # 压缩图像
                                with Image.open(temp_img_path) as img:
                                    # 转换为RGB模式
                                    if img.mode in ('RGBA', 'LA', 'P'):
                                        img = img.convert('RGB')
                                    
                                    # 调整尺寸
                                    if img.width > max_width:
                                        ratio = max_width / img.width
                                        new_height = int(img.height * ratio)
                                        img = img.resize((max_width, new_height), Image.Resampling.LANCZOS)
                                    
                                    # 保存压缩后的图像
                                    compressed_img_path = Path(self.temp_dir) / f"compressed_{page_num}_{obj}.jpg"
                                    img.save(compressed_img_path, 'JPEG', quality=image_quality, optimize=True)
                                
                            except Exception as e:
                                print(f"   ⚠️ 处理图像失败: {e}")
                                continue
            
            # 保存压缩后的PDF
            with open(output_path, 'wb') as f:
                writer.write(f)
            
            return True
            
        except Exception as e:
            print(f"   ❌ 图像压缩失败: {e}")
            return False
    
    def remove_metadata(self, input_path: Path, output_path: Path) -> bool:
        """
        移除PDF元数据以减小文件大小
        """
        try:
            reader = PdfReader(str(input_path))
            writer = PdfWriter()
            
            # 复制所有页面
            for page in reader.pages:
                writer.add_page(page)
            
            # 移除元数据和链接（兼容不同版本）
            if hasattr(writer, 'remove_links'):
                writer.remove_links()
            elif hasattr(writer, 'removeLinks'):
                writer.removeLinks()
            
            # 保存
            with open(output_path, 'wb') as f:
                writer.write(f)
            
            return True
            
        except Exception as e:
            print(f"   ❌ 移除元数据失败: {e}")
            return False
    
    def compress_with_pypdf2(self, input_path: Path, output_path: Path) -> bool:
        """
        使用PyPDF2进行基础压缩
        """
        try:
            reader = PdfReader(str(input_path))
            writer = PdfWriter()
            
            # 复制页面并应用压缩
            for page in reader.pages:
                page.compress_content_streams()  # 压缩内容流
                writer.add_page(page)
            
            # 保存
            with open(output_path, 'wb') as f:
                writer.write(f)
            
            return True
            
        except Exception as e:
            print(f"   ❌ PyPDF2压缩失败: {e}")
            return False
    
    def optimize_pdf_structure(self, input_path: Path, output_path: Path) -> bool:
        """
        优化PDF结构
        """
        try:
            reader = PdfReader(str(input_path))
            writer = PdfWriter()

            # 复制页面
            for page in reader.pages:
                writer.add_page(page)

            # 移除重复对象（如果方法存在）
            if hasattr(writer, 'remove_duplication'):
                writer.remove_duplication()
            elif hasattr(writer, 'removeLinks'):
                writer.removeLinks()

            # 保存
            with open(output_path, 'wb') as f:
                writer.write(f)

            return True

        except Exception as e:
            print(f"   ❌ 结构优化失败: {e}")
            return False

    def compress_with_ghostscript(self, input_path: Path, output_path: Path,
                                 quality: str = "ebook") -> bool:
        """
        使用Ghostscript进行高级压缩（如果可用）

        Args:
            input_path: 输入PDF路径
            output_path: 输出PDF路径
            quality: 质量级别 ("screen", "ebook", "printer", "prepress")
        """
        if not GHOSTSCRIPT_AVAILABLE:
            return False

        try:
            # 检查Ghostscript是否安装
            gs_command = "gs"  # Linux/Mac
            if os.name == 'nt':  # Windows
                gs_command = "gswin64c"

            # 构建Ghostscript命令
            cmd = [
                gs_command,
                "-sDEVICE=pdfwrite",
                "-dCompatibilityLevel=1.4",
                f"-dPDFSETTINGS=/{quality}",
                "-dNOPAUSE",
                "-dQUIET",
                "-dBATCH",
                "-dColorImageResolution=150",
                "-dGrayImageResolution=150",
                "-dMonoImageResolution=150",
                f"-sOutputFile={output_path}",
                str(input_path)
            ]

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                return True
            else:
                print(f"   ⚠️ Ghostscript压缩失败: {result.stderr}")
                return False

        except FileNotFoundError:
            print("   ⚠️ Ghostscript未安装，跳过高级压缩")
            return False
        except Exception as e:
            print(f"   ❌ Ghostscript压缩出错: {e}")
            return False
    
    def compress_pdf(self, input_path: Path, output_path: Path, 
                    compression_level: str = "medium") -> Tuple[bool, float, float]:
        """
        压缩PDF文件
        
        Args:
            input_path: 输入PDF路径
            output_path: 输出PDF路径
            compression_level: 压缩级别 ("low", "medium", "high", "extreme")
        
        Returns:
            Tuple[bool, float, float]: (成功状态, 原始大小MB, 压缩后大小MB)
        """
        if not input_path.exists():
            print(f"❌ 输入文件不存在: {input_path}")
            return False, 0, 0
        
        original_size = self.get_file_size_mb(input_path)
        print(f"📄 压缩文件: {input_path.name}")
        print(f"   原始大小: {original_size:.2f} MB")
        
        # 根据压缩级别设置参数
        compression_params = {
            "low": {"image_quality": 80, "max_width": 1600},
            "medium": {"image_quality": 60, "max_width": 1200},
            "high": {"image_quality": 40, "max_width": 800},
            "extreme": {"image_quality": 30, "max_width": 600}
        }
        
        params = compression_params.get(compression_level, compression_params["medium"])
        
        try:
            # 创建临时文件路径
            temp_files = [
                Path(self.temp_dir) / "step1_basic.pdf",
                Path(self.temp_dir) / "step2_images.pdf",
                Path(self.temp_dir) / "step3_metadata.pdf",
                Path(self.temp_dir) / "step4_optimized.pdf",
                Path(self.temp_dir) / "step5_ghostscript.pdf"
            ]

            current_input = input_path

            # 步骤1: 基础压缩
            print("   🔧 步骤1: 基础压缩...")
            if self.compress_with_pypdf2(current_input, temp_files[0]):
                current_input = temp_files[0]
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")

            # 步骤2: 图像压缩
            print("   🖼️ 步骤2: 图像压缩...")
            if self.compress_images_in_pdf(current_input, temp_files[1],
                                         params["image_quality"], params["max_width"]):
                current_input = temp_files[1]
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")

            # 步骤3: 移除元数据
            print("   🗑️ 步骤3: 移除元数据...")
            if self.remove_metadata(current_input, temp_files[2]):
                current_input = temp_files[2]
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")

            # 步骤4: 结构优化
            print("   ⚙️ 步骤4: 结构优化...")
            if self.optimize_pdf_structure(current_input, temp_files[3]):
                current_input = temp_files[3]
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")

            # 步骤5: Ghostscript高级压缩（如果可用）
            print("   🚀 步骤5: 高级压缩...")
            gs_quality = "ebook" if compression_level in ["medium", "high"] else "screen"
            if compression_level == "extreme":
                gs_quality = "screen"

            if self.compress_with_ghostscript(current_input, temp_files[4], gs_quality):
                current_input = temp_files[4]
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")
            else:
                print("      跳过Ghostscript压缩")
            
            # 复制最终结果
            shutil.copy2(current_input, output_path)
            final_size = self.get_file_size_mb(output_path)
            
            # 计算压缩比
            compression_ratio = final_size / original_size if original_size > 0 else 0
            space_saved = original_size - final_size
            
            print(f"   ✅ 压缩完成!")
            print(f"   📊 压缩统计:")
            print(f"      原始大小: {original_size:.2f} MB")
            print(f"      压缩后大小: {final_size:.2f} MB")
            print(f"      压缩比: {compression_ratio:.1%}")
            print(f"      节省空间: {space_saved:.2f} MB")
            
            return True, original_size, final_size
            
        except Exception as e:
            print(f"   ❌ 压缩失败: {e}")
            return False, original_size, 0
    
    def batch_compress(self, input_dir: Path, output_dir: Path = None, 
                      compression_level: str = "medium") -> dict:
        """
        批量压缩PDF文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录（默认为输入目录下的compressed子目录）
            compression_level: 压缩级别
        
        Returns:
            dict: 压缩统计信息
        """
        if output_dir is None:
            output_dir = input_dir / "compressed"
        
        output_dir.mkdir(exist_ok=True)
        
        # 查找所有PDF文件
        pdf_files = list(input_dir.rglob("*.pdf"))
        
        if not pdf_files:
            print("❌ 没有找到PDF文件")
            return {}
        
        print(f"🔍 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 60)
        
        stats = {
            "total_files": len(pdf_files),
            "successful": 0,
            "failed": 0,
            "total_original_size": 0,
            "total_compressed_size": 0
        }
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"\n📚 处理文件 {i}/{len(pdf_files)}")
            
            # 生成输出文件路径
            relative_path = pdf_file.relative_to(input_dir)
            output_file = output_dir / relative_path
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 压缩文件
            success, original_size, compressed_size = self.compress_pdf(
                pdf_file, output_file, compression_level
            )
            
            if success:
                stats["successful"] += 1
                stats["total_original_size"] += original_size
                stats["total_compressed_size"] += compressed_size
            else:
                stats["failed"] += 1
        
        # 打印总体统计
        print("\n" + "=" * 60)
        print("📊 批量压缩统计:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   成功压缩: {stats['successful']}")
        print(f"   失败数量: {stats['failed']}")
        
        if stats["total_original_size"] > 0:
            total_ratio = stats["total_compressed_size"] / stats["total_original_size"]
            total_saved = stats["total_original_size"] - stats["total_compressed_size"]
            
            print(f"   原始总大小: {stats['total_original_size']:.2f} MB")
            print(f"   压缩后总大小: {stats['total_compressed_size']:.2f} MB")
            print(f"   总体压缩比: {total_ratio:.1%}")
            print(f"   总节省空间: {total_saved:.2f} MB")
        
        print(f"   输出目录: {output_dir}")
        
        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PDF压缩工具')
    parser.add_argument('input', help='输入PDF文件或目录')
    parser.add_argument('output', nargs='?', help='输出PDF文件或目录')
    parser.add_argument('--batch', '-b', action='store_true', help='批量处理模式')
    parser.add_argument('--level', '-l', choices=['low', 'medium', 'high', 'extreme'], 
                       default='medium', help='压缩级别')
    parser.add_argument('--target-ratio', '-r', type=float, default=0.2, 
                       help='目标压缩比例（默认0.2）')
    
    args = parser.parse_args()
    
    input_path = Path(args.input)
    
    if not input_path.exists():
        print(f"❌ 输入路径不存在: {input_path}")
        sys.exit(1)
    
    print("🗜️ PDF压缩工具")
    print("=" * 50)
    print(f"📂 输入: {input_path}")
    print(f"🎯 目标压缩比: {args.target_ratio:.1%}")
    print(f"⚙️ 压缩级别: {args.level}")
    
    try:
        with PDFCompressor(args.target_ratio) as compressor:
            if args.batch or input_path.is_dir():
                # 批量处理模式
                output_dir = Path(args.output) if args.output else None
                stats = compressor.batch_compress(input_path, output_dir, args.level)
                
            else:
                # 单文件处理模式
                if not args.output:
                    output_path = input_path.parent / f"{input_path.stem}_compressed.pdf"
                else:
                    output_path = Path(args.output)
                
                print(f"📄 输出: {output_path}")
                print()
                
                success, original_size, compressed_size = compressor.compress_pdf(
                    input_path, output_path, args.level
                )
                
                if not success:
                    sys.exit(1)
        
        print("\n✅ 压缩完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
