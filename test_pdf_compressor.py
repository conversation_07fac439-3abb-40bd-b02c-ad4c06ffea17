#!/usr/bin/env python3
"""
PDF压缩工具测试脚本

用于测试PDF压缩功能的各个组件
"""

import os
import tempfile
from pathlib import Path
import subprocess
from pdf_compressor import PDFCompressor

def check_dependencies():
    """检查依赖包是否安装"""
    print("🔍 检查依赖包...")
    
    dependencies = {
        "PyPDF2": "PyPDF2",
        "Pillow": "PIL",
        "reportlab": "reportlab"
    }
    
    missing = []
    
    for name, module in dependencies.items():
        try:
            __import__(module)
            print(f"   ✅ {name}: 已安装")
        except ImportError:
            print(f"   ❌ {name}: 未安装")
            missing.append(name)
    
    # 检查Ghostscript
    try:
        gs_command = "gs" if os.name != 'nt' else "gswin64c"
        result = subprocess.run([gs_command, "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip().split('\n')[0]
            print(f"   ✅ Ghostscript: {version}")
        else:
            print("   ⚠️ Ghostscript: 未安装（可选，但推荐安装以获得更好的压缩效果）")
    except FileNotFoundError:
        print("   ⚠️ Ghostscript: 未安装（可选，但推荐安装以获得更好的压缩效果）")
    
    if missing:
        print(f"\n❌ 缺少依赖包: {', '.join(missing)}")
        print("请运行以下命令安装:")
        for pkg in missing:
            print(f"   pip install {pkg}")
        return False
    
    return True

def create_test_pdf():
    """创建一个测试PDF文件"""
    print("\n📄 创建测试PDF文件...")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.lib.utils import ImageReader
        from PIL import Image
        
        # 创建临时目录
        temp_dir = Path(tempfile.mkdtemp(prefix="pdf_test_"))
        pdf_path = temp_dir / "test_document.pdf"
        
        # 创建PDF
        c = canvas.Canvas(str(pdf_path), pagesize=A4)
        width, height = A4
        
        # 添加多页内容
        for page in range(1, 6):  # 5页
            # 添加标题
            c.setFont("Helvetica-Bold", 24)
            c.drawString(50, height - 100, f"测试文档 - 第{page}页")
            
            # 添加正文
            c.setFont("Helvetica", 12)
            y_position = height - 150
            
            text_content = [
                "这是一个用于测试PDF压缩功能的示例文档。",
                "本文档包含多种元素：文本、图像和格式。",
                "",
                "PDF压缩测试内容：",
                "• 文本压缩测试",
                "• 图像压缩测试", 
                "• 元数据优化测试",
                "• 结构优化测试",
                "",
                f"当前页码：{page}/5",
                f"文件大小优化目标：压缩到原来的20%",
                "",
                "重复内容用于测试压缩效果：" * 10,
            ]
            
            for line in text_content:
                c.drawString(50, y_position, line)
                y_position -= 20
                if y_position < 100:
                    break
            
            # 创建一个简单的测试图像
            if page <= 3:  # 前3页添加图像
                try:
                    # 创建一个彩色测试图像
                    img = Image.new('RGB', (200, 150), color=(100 + page * 30, 150, 200))
                    
                    # 添加一些图案
                    from PIL import ImageDraw
                    draw = ImageDraw.Draw(img)
                    for i in range(0, 200, 20):
                        draw.line([(i, 0), (i, 150)], fill=(255, 255, 255), width=1)
                    for i in range(0, 150, 20):
                        draw.line([(0, i), (200, i)], fill=(255, 255, 255), width=1)
                    
                    # 保存临时图像
                    img_path = temp_dir / f"test_image_{page}.png"
                    img.save(img_path, 'PNG')
                    
                    # 添加到PDF
                    c.drawImage(str(img_path), 300, height - 300, width=200, height=150)
                    
                except Exception as e:
                    print(f"   ⚠️ 添加图像失败: {e}")
            
            # 添加页脚
            c.setFont("Helvetica", 10)
            c.drawString(50, 50, f"PDF压缩测试文档 - 页面 {page}")
            c.drawString(width - 150, 50, "测试时间: 2024-01-15")
            
            c.showPage()
        
        c.save()
        
        file_size = pdf_path.stat().st_size / (1024 * 1024)
        print(f"   ✅ 测试PDF创建成功: {pdf_path}")
        print(f"   📊 文件大小: {file_size:.2f} MB")
        
        return pdf_path
        
    except Exception as e:
        print(f"   ❌ 创建测试PDF失败: {e}")
        return None

def test_compression_levels(test_pdf_path: Path):
    """测试不同压缩级别"""
    print(f"\n🗜️ 测试不同压缩级别...")
    print("-" * 60)
    
    levels = ["low", "medium", "high", "extreme"]
    results = {}
    
    for level in levels:
        print(f"\n📊 测试压缩级别: {level}")
        
        output_path = test_pdf_path.parent / f"compressed_{level}.pdf"
        
        try:
            with PDFCompressor() as compressor:
                success, original_size, compressed_size = compressor.compress_pdf(
                    test_pdf_path, output_path, level
                )
                
                if success:
                    compression_ratio = compressed_size / original_size
                    results[level] = {
                        "original_size": original_size,
                        "compressed_size": compressed_size,
                        "ratio": compression_ratio,
                        "space_saved": original_size - compressed_size
                    }
                else:
                    results[level] = None
                    
        except Exception as e:
            print(f"   ❌ 压缩失败: {e}")
            results[level] = None
    
    # 打印对比结果
    print(f"\n📈 压缩效果对比:")
    print("-" * 80)
    print(f"{'级别':<10} {'原始大小':<12} {'压缩后':<12} {'压缩比':<10} {'节省空间':<12}")
    print("-" * 80)
    
    for level, result in results.items():
        if result:
            print(f"{level:<10} {result['original_size']:<12.2f} "
                  f"{result['compressed_size']:<12.2f} {result['ratio']:<10.1%} "
                  f"{result['space_saved']:<12.2f}")
        else:
            print(f"{level:<10} {'失败':<12} {'失败':<12} {'失败':<10} {'失败':<12}")
    
    print("-" * 80)
    
    # 找出最佳压缩级别
    valid_results = {k: v for k, v in results.items() if v is not None}
    if valid_results:
        best_level = min(valid_results.keys(), 
                        key=lambda x: valid_results[x]['compressed_size'])
        best_result = valid_results[best_level]
        
        print(f"🏆 最佳压缩级别: {best_level}")
        print(f"   压缩比: {best_result['ratio']:.1%}")
        print(f"   节省空间: {best_result['space_saved']:.2f} MB")
        
        # 检查是否达到目标压缩比（20%）
        if best_result['ratio'] <= 0.25:  # 25%以内认为接近目标
            print("   ✅ 达到目标压缩比（≤25%）")
        else:
            print("   ⚠️ 未达到目标压缩比（20%），可能需要调整参数")
    
    return results

def test_batch_compression():
    """测试批量压缩功能"""
    print(f"\n📚 测试批量压缩功能...")
    print("-" * 60)
    
    try:
        # 创建测试目录结构
        temp_dir = Path(tempfile.mkdtemp(prefix="batch_test_"))
        
        # 创建多个测试PDF
        test_pdfs = []
        for i in range(3):
            pdf_path = create_test_pdf()
            if pdf_path:
                new_path = temp_dir / f"test_document_{i+1}.pdf"
                pdf_path.rename(new_path)
                test_pdfs.append(new_path)
        
        if not test_pdfs:
            print("   ❌ 无法创建测试PDF文件")
            return
        
        print(f"   📄 创建了 {len(test_pdfs)} 个测试PDF文件")
        
        # 执行批量压缩
        with PDFCompressor() as compressor:
            stats = compressor.batch_compress(temp_dir, compression_level="medium")
        
        print(f"\n✅ 批量压缩测试完成")
        return stats
        
    except Exception as e:
        print(f"   ❌ 批量压缩测试失败: {e}")
        return None

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🧪 测试边界情况...")
    print("-" * 60)
    
    # 测试空文件
    print("   📝 测试不存在的文件...")
    with PDFCompressor() as compressor:
        success, _, _ = compressor.compress_pdf(
            Path("nonexistent.pdf"), Path("output.pdf")
        )
        if not success:
            print("   ✅ 正确处理不存在的文件")
        else:
            print("   ❌ 应该失败但成功了")

def main():
    """运行所有测试"""
    print("🧪 PDF压缩工具测试套件")
    print("=" * 60)
    
    try:
        # 检查依赖
        if not check_dependencies():
            return
        
        # 创建测试PDF
        test_pdf = create_test_pdf()
        if not test_pdf:
            print("❌ 无法创建测试PDF，终止测试")
            return
        
        # 测试压缩级别
        compression_results = test_compression_levels(test_pdf)
        
        # 测试批量压缩
        batch_results = test_batch_compression()
        
        # 测试边界情况
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成!")
        
        print("\n💡 使用建议:")
        print("   1. 对于一般文档，推荐使用 'medium' 压缩级别")
        print("   2. 对于图像较多的PDF，可以尝试 'high' 或 'extreme' 级别")
        print("   3. 安装Ghostscript可以获得更好的压缩效果")
        print("   4. 大文件建议先测试单个文件再批量处理")
        
        print("\n🚀 开始使用:")
        print("   python pdf_compressor.py input.pdf output.pdf")
        print("   python pdf_compressor.py --batch /path/to/pdf/directory")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
