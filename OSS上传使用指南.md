# OSS上传工具使用指南

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install oss2
```

### 2. 配置OSS认证信息

#### 方法1：使用环境变量（推荐）
```bash
# 复制配置模板
cp oss_config_example.sh oss_config.sh

# 编辑配置文件，填入你的真实信息
nano oss_config.sh

# 加载配置
source oss_config.sh
```

#### 方法2：使用命令行参数
```bash
python upload_mp4_to_oss.py /path/to/videos \
  --access-key-id "your_access_key_id" \
  --access-key-secret "your_access_key_secret" \
  --endpoint "https://oss-cn-hangzhou.aliyuncs.com" \
  --bucket-name "your_bucket_name"
```

### 3. 预览要上传的文件
```bash
python upload_mp4_to_oss.py /path/to/videos --dry-run
```

### 4. 开始上传
```bash
python upload_mp4_to_oss.py /path/to/videos
```

## 📋 OSS Key生成规则

工具会根据文件路径自动生成OSS key，规则如下：

**规则：** `video` + 文件所在目录往上4级 + 文件名

**示例：**
```
本地文件路径: /Volumes/WD_BLACK/人教_副本/八年级/上册/14.3.2.1_因式分解/lesson1.mp4
生成的OSS Key: video/八年级/上册/14.3.2.1_因式分解/lesson1.mp4

本地文件路径: /data/videos/2023/math/grade8/chapter1/video.mp4  
生成的OSS Key: video/2023/math/grade8/chapter1/video.mp4
```

## 📊 CSV记录文件

上传成功后，会在CSV文件中记录以下信息：

| 字段 | 说明 | 示例 |
|------|------|------|
| uuid | 生成的唯一标识符 | 550e8400-e29b-41d4-a716-************ |
| filename | 视频文件名 | lesson1.mp4 |
| oss_key | OSS对象key | video/八年级/上册/14.3.2.1_因式分解/lesson1.mp4 |
| file_size_mb | 文件大小(MB) | 125.67 |
| file_hash | 文件MD5哈希值 | d41d8cd98f00b204e9800998ecf8427e |
| file_path | 本地文件路径 | /data/videos/八年级/上册/14.3.2.1_因式分解/lesson1.mp4 |
| upload_time | 上传时间 | 2024-01-15 14:30:25 |
| status | 上传状态 | completed/failed |

**CSV文件示例：**
```csv
uuid,filename,oss_key,file_size_mb,file_hash,file_path,upload_time,status
550e8400-e29b-41d4-a716-************,lesson1.mp4,video/八年级/上册/14.3.2.1_因式分解/lesson1.mp4,125.67,d41d8cd98f00b204e9800998ecf8427e,/data/videos/八年级/上册/14.3.2.1_因式分解/lesson1.mp4,2024-01-15 14:30:25,completed
6ba7b810-9dad-11d1-80b4-00c04fd430c8,lesson2.mp4,video/八年级/上册/14.3.2.1_因式分解/lesson2.mp4,98.43,e3b0c44298fc1c149afbf4c8996fb924,/data/videos/八年级/上册/14.3.2.1_因式分解/lesson2.mp4,2024-01-15 14:32:10,completed
```

## 🎯 常用命令

### 预览模式
```bash
# 查看将要上传的文件和生成的OSS key
python upload_mp4_to_oss.py /path/to/videos --dry-run
```

### 指定CSV文件名
```bash
python upload_mp4_to_oss.py /path/to/videos --csv-file my_uploads.csv
```

### 覆盖已存在的文件
```bash
python upload_mp4_to_oss.py /path/to/videos --overwrite
```

### 设置文件访问权限
```bash
# 私有文件（默认）
python upload_mp4_to_oss.py /path/to/videos --acl private

# 公开读取
python upload_mp4_to_oss.py /path/to/videos --acl public-read

# 公开读写
python upload_mp4_to_oss.py /path/to/videos --acl public-read-write
```

### 断点续传功能
```bash
# 启用断点续传（默认）- 跳过已上传的文件
python upload_mp4_to_oss.py /path/to/videos

# 禁用断点续传 - 重新上传所有文件
python upload_mp4_to_oss.py /path/to/videos --no-resume

# 强制覆盖已存在的文件
python upload_mp4_to_oss.py /path/to/videos --overwrite
```

### 完整命令示例
```bash
python upload_mp4_to_oss.py "/Volumes/WD_BLACK/人教_副本/八年级" \
  --csv-file "grade8_uploads.csv" \
  --dry-run
```

## ⚙️ 配置说明

### OSS Endpoint选择
根据你的bucket所在区域选择对应的endpoint：

| 区域 | Endpoint |
|------|----------|
| 华东1（杭州） | https://oss-cn-hangzhou.aliyuncs.com |
| 华东2（上海） | https://oss-cn-shanghai.aliyuncs.com |
| 华北1（青岛） | https://oss-cn-qingdao.aliyuncs.com |
| 华北2（北京） | https://oss-cn-beijing.aliyuncs.com |
| 华南1（深圳） | https://oss-cn-shenzhen.aliyuncs.com |

### AccessKey获取
1. 登录阿里云控制台
2. 访问 RAM 访问控制
3. 创建用户并授予OSS相关权限
4. 创建AccessKey

### 文件访问权限说明
| 权限类型 | 说明 | 适用场景 |
|---------|------|----------|
| `private` | 私有读写（默认） | 需要授权才能访问的文件，如付费课程视频 |
| `public-read` | 公开读取，私有写入 | 可以公开访问的文件，如免费课程视频 |
| `public-read-write` | 公开读写 | 允许任何人读取和修改的文件（不推荐） |

## 🔄 断点续传功能

### 功能说明
- **自动检测**：程序会自动检测已上传的文件，避免重复上传
- **文件完整性**：通过MD5哈希值验证文件是否已更改
- **状态记录**：记录每个文件的上传状态（completed/failed）
- **智能跳过**：跳过已成功上传且未更改的文件

### 工作原理
1. **加载历史记录**：读取CSV文件中的上传历史
2. **文件哈希比较**：计算当前文件的MD5哈希值与记录比较
3. **状态检查**：只跳过状态为"completed"的文件
4. **实时更新**：上传过程中实时更新记录

### 使用场景
- **大批量上传**：上传大量文件时，中途中断可以从断点继续
- **增量更新**：定期上传新增的视频文件
- **错误恢复**：网络中断或其他错误后，重新运行只上传失败的文件

**推荐设置：**
- 教育视频内容：使用 `private`（默认），通过应用程序控制访问权限
- 公开宣传视频：使用 `public-read`

## 🔧 功能特点

### 智能文件过滤
- 自动跳过系统文件（如`._`开头的文件）
- 跳过小于1MB的文件
- 递归扫描所有子目录

### 上传进度显示
- 实时显示单个文件上传进度
- 显示总体上传进度
- 显示上传速度统计

### 错误处理
- 自动检测OSS连接状态
- 处理网络异常和重试
- 详细的错误信息记录

### 安全特性
- 支持环境变量配置，避免密钥泄露
- 可选择是否覆盖已存在文件
- 预览模式确认上传内容

## 📝 使用示例

### 示例1：预览上传
```bash
# 加载配置
source oss_config.sh

# 预览要上传的文件
python upload_mp4_to_oss.py "/Volumes/WD_BLACK/人教_副本/八年级" --dry-run
```

输出示例：
```
============================================================
🚀 MP4文件批量上传到阿里云OSS
============================================================
📂 本地目录: /Volumes/WD_BLACK/人教_副本/八年级
🪣 OSS Bucket: my-video-bucket
📝 CSV文件: oss_files.csv
🔍 运行模式: 预览模式
============================================================
📁 找到 3 个有效的 .mp4 文件
📊 总文件大小: 968.1 MB

[1/3] 📹 因式分解—公式法（第一课时）_视频.mp4 (210.9 MB)
  UUID: 550e8400-e29b-41d4-a716-************
  OSS Key: video/八年级/上册/14.3.2.1_因式分解/因式分解—公式法（第一课时）_视频.mp4
  本地路径: /Volumes/WD_BLACK/人教_副本/八年级/上册/14.3.2.1_因式分解/因式分解—公式法（第一课时）_视频.mp4
```

### 示例2：实际上传
```bash
# 执行上传
python upload_mp4_to_oss.py "/Volumes/WD_BLACK/人教_副本/八年级" --csv-file "grade8_videos.csv"
```

## ⚠️ 注意事项

1. **网络稳定性**：确保网络连接稳定，大文件上传可能需要较长时间
2. **存储空间**：确保OSS bucket有足够的存储空间
3. **权限配置**：确保AccessKey有OSS读写权限
4. **文件备份**：建议在上传前备份重要文件
5. **成本控制**：注意OSS的存储和流量费用

## 🔍 故障排除

### 常见错误及解决方案

**1. OSS连接失败**
- 检查AccessKey是否正确
- 检查endpoint是否匹配bucket区域
- 检查网络连接

**2. 权限不足**
- 确保AccessKey有OSS PutObject权限
- 检查bucket的访问控制设置

**3. 文件上传失败**
- 检查文件是否损坏
- 检查文件大小是否超过OSS限制
- 检查网络稳定性

**4. CSV文件写入失败**
- 检查当前目录的写权限
- 确保CSV文件没有被其他程序占用

## 📈 性能优化建议

1. **分批上传**：对于大量文件，建议分批处理
2. **网络优化**：使用稳定的网络环境
3. **并发控制**：避免同时运行多个上传任务
4. **监控进度**：使用预览模式确认上传计划

这个工具可以帮助你高效地将MP4文件批量上传到阿里云OSS，并自动生成详细的记录文件。
