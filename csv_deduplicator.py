#!/usr/bin/env python3
"""
CSV文件去重工具
用于处理oss_files.csv文件的重复数据

规则：
1. 如果第一列(uuid)和第二列(filename)都重复，删除重复数据，只保留1条
2. 如果第一列或第二列单独重复，列出这些数据供检查
"""

import csv
import sys
from pathlib import Path
from collections import defaultdict, Counter
from typing import List, Dict, Tuple, Set

def read_csv_robust(file_path: Path) -> Tuple[List[str], List[List[str]]]:
    """
    鲁棒地读取CSV文件，处理格式不规范的行
    
    Returns:
        Tuple[List[str], List[List[str]]]: (headers, rows)
    """
    headers = []
    rows = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    if not lines:
        return headers, rows
    
    # 读取表头
    headers = [col.strip() for col in lines[0].strip().split(',')]
    expected_cols = len(headers)
    
    print(f"📋 CSV表头: {headers}")
    print(f"📊 预期列数: {expected_cols}")
    print()
    
    # 读取数据行
    for line_num, line in enumerate(lines[1:], 2):
        line = line.strip()
        if not line:
            continue
            
        # 尝试解析CSV行
        try:
            # 使用csv.reader来正确处理引号等特殊字符
            reader = csv.reader([line])
            row = next(reader)
            
            if len(row) == expected_cols:
                rows.append(row)
            elif len(row) > expected_cols:
                # 行数据过多，可能是格式问题
                print(f"⚠️  第{line_num}行列数过多 ({len(row)} > {expected_cols}): {line[:100]}...")
                # 尝试取前面的列
                rows.append(row[:expected_cols])
            else:
                # 行数据不足
                print(f"⚠️  第{line_num}行列数不足 ({len(row)} < {expected_cols}): {line[:100]}...")
                # 补充空列
                while len(row) < expected_cols:
                    row.append('')
                rows.append(row)
                
        except Exception as e:
            print(f"❌ 第{line_num}行解析失败: {e}")
            print(f"   内容: {line[:100]}...")
            continue
    
    print(f"✅ 成功读取 {len(rows)} 行数据")
    return headers, rows

def analyze_duplicates(headers: List[str], rows: List[List[str]]) -> Dict:
    """
    分析重复数据
    
    Returns:
        Dict: 包含各种重复情况的分析结果
    """
    if len(headers) < 2:
        print("❌ CSV文件列数不足，无法进行重复分析")
        return {}
    
    # 统计数据
    uuid_count = Counter()
    filename_count = Counter()
    uuid_filename_pairs = []
    uuid_filename_count = Counter()
    
    # 记录每行数据
    for i, row in enumerate(rows):
        if len(row) >= 2:
            uuid = row[0].strip()
            filename = row[1].strip()
            
            uuid_count[uuid] += 1
            filename_count[filename] += 1
            
            pair = (uuid, filename)
            uuid_filename_pairs.append((i, pair, row))
            uuid_filename_count[pair] += 1
    
    # 分析结果
    result = {
        'total_rows': len(rows),
        'uuid_duplicates': {k: v for k, v in uuid_count.items() if v > 1},
        'filename_duplicates': {k: v for k, v in filename_count.items() if v > 1},
        'uuid_filename_duplicates': {k: v for k, v in uuid_filename_count.items() if v > 1},
        'uuid_filename_pairs': uuid_filename_pairs
    }
    
    return result

def print_analysis_report(analysis: Dict):
    """打印分析报告"""
    print("=" * 80)
    print("📊 重复数据分析报告")
    print("=" * 80)
    
    print(f"📋 总数据行数: {analysis['total_rows']}")
    print()
    
    # UUID重复
    uuid_dups = analysis['uuid_duplicates']
    print(f"🔍 UUID重复情况: {len(uuid_dups)} 个UUID有重复")
    if uuid_dups:
        for uuid, count in list(uuid_dups.items())[:5]:  # 只显示前5个
            print(f"   UUID: {uuid[:20]}... (重复 {count} 次)")
        if len(uuid_dups) > 5:
            print(f"   ... 还有 {len(uuid_dups) - 5} 个重复的UUID")
    print()
    
    # 文件名重复
    filename_dups = analysis['filename_duplicates']
    print(f"📁 文件名重复情况: {len(filename_dups)} 个文件名有重复")
    if filename_dups:
        for filename, count in list(filename_dups.items())[:5]:  # 只显示前5个
            print(f"   文件名: {filename} (重复 {count} 次)")
        if len(filename_dups) > 5:
            print(f"   ... 还有 {len(filename_dups) - 5} 个重复的文件名")
    print()
    
    # UUID+文件名组合重复
    pair_dups = analysis['uuid_filename_duplicates']
    print(f"🎯 UUID+文件名组合重复: {len(pair_dups)} 个组合有重复")
    if pair_dups:
        for (uuid, filename), count in list(pair_dups.items())[:3]:  # 只显示前3个
            print(f"   UUID: {uuid[:20]}...")
            print(f"   文件名: {filename}")
            print(f"   重复次数: {count}")
            print()

def generate_deduplicated_data(headers: List[str], analysis: Dict) -> List[List[str]]:
    """
    生成去重后的数据
    
    规则：如果UUID+文件名组合重复，只保留第一条记录
    """
    seen_pairs = set()
    deduplicated_rows = []
    removed_rows = []
    
    for row_index, (uuid, filename), row in analysis['uuid_filename_pairs']:
        pair = (uuid, filename)
        
        if pair not in seen_pairs:
            seen_pairs.add(pair)
            deduplicated_rows.append(row)
        else:
            removed_rows.append((row_index + 2, row))  # +2 因为有表头和从0开始计数
    
    print(f"🗑️  移除了 {len(removed_rows)} 条重复记录")
    if removed_rows:
        print("移除的记录:")
        for line_num, row in removed_rows[:5]:  # 只显示前5条
            uuid = row[0][:20] + "..." if len(row[0]) > 20 else row[0]
            filename = row[1] if len(row) > 1 else "N/A"
            print(f"   第{line_num}行: UUID={uuid}, 文件名={filename}")
        if len(removed_rows) > 5:
            print(f"   ... 还有 {len(removed_rows) - 5} 条记录")
    
    return deduplicated_rows

def save_results(headers: List[str], deduplicated_rows: List[List[str]], 
                analysis: Dict, input_file: Path):
    """保存处理结果"""
    
    # 保存去重后的CSV
    output_file = input_file.parent / f"{input_file.stem}_deduplicated.csv"
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(headers)
        writer.writerows(deduplicated_rows)
    
    print(f"✅ 去重后的数据已保存到: {output_file}")
    print(f"📊 原始数据: {analysis['total_rows']} 行")
    print(f"📊 去重后数据: {len(deduplicated_rows)} 行")
    print(f"📊 移除重复: {analysis['total_rows'] - len(deduplicated_rows)} 行")
    
    # 保存重复数据报告
    report_file = input_file.parent / f"{input_file.stem}_duplicate_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("重复数据分析报告\n")
        f.write("=" * 50 + "\n\n")
        
        f.write(f"总数据行数: {analysis['total_rows']}\n\n")
        
        # UUID重复详情
        f.write("UUID重复详情:\n")
        f.write("-" * 30 + "\n")
        for uuid, count in analysis['uuid_duplicates'].items():
            f.write(f"UUID: {uuid} (重复 {count} 次)\n")
        f.write("\n")
        
        # 文件名重复详情
        f.write("文件名重复详情:\n")
        f.write("-" * 30 + "\n")
        for filename, count in analysis['filename_duplicates'].items():
            f.write(f"文件名: {filename} (重复 {count} 次)\n")
        f.write("\n")
        
        # UUID+文件名组合重复详情
        f.write("UUID+文件名组合重复详情:\n")
        f.write("-" * 30 + "\n")
        for (uuid, filename), count in analysis['uuid_filename_duplicates'].items():
            f.write(f"UUID: {uuid}\n")
            f.write(f"文件名: {filename}\n")
            f.write(f"重复次数: {count}\n")
            f.write("-" * 20 + "\n")
    
    print(f"📄 重复数据报告已保存到: {report_file}")

def main():
    """主函数"""
    print("🔧 CSV文件去重工具")
    print("=" * 50)
    
    # 检查输入文件
    input_file = Path("oss_files.csv")
    if not input_file.exists():
        print(f"❌ 文件不存在: {input_file}")
        sys.exit(1)
    
    print(f"📂 处理文件: {input_file}")
    print(f"📊 文件大小: {input_file.stat().st_size / 1024:.1f} KB")
    print()
    
    try:
        # 读取CSV文件
        print("📖 读取CSV文件...")
        headers, rows = read_csv_robust(input_file)
        
        if not rows:
            print("❌ 没有找到有效数据")
            return
        
        # 分析重复数据
        print("\n🔍 分析重复数据...")
        analysis = analyze_duplicates(headers, rows)
        
        # 打印分析报告
        print_analysis_report(analysis)
        
        # 生成去重数据
        print("🧹 生成去重数据...")
        deduplicated_rows = generate_deduplicated_data(headers, analysis)
        
        # 保存结果
        print("\n💾 保存处理结果...")
        save_results(headers, deduplicated_rows, analysis, input_file)
        
        print("\n✅ 处理完成！")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
