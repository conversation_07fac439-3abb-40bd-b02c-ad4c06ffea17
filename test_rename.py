#!/usr/bin/env python3
"""
测试脚本：创建一些测试用的 .mp4 文件，然后测试重命名功能
"""

import os
import tempfile
import shutil
from pathlib import Path
from rename_mp4_to_ts import rename_mp4_to_ts


def create_test_files():
    """创建测试用的目录结构和 .mp4 文件"""
    # 创建临时目录
    test_dir = Path(tempfile.mkdtemp(prefix="mp4_test_"))
    print(f"创建测试目录: {test_dir}")
    
    # 创建子目录结构
    (test_dir / "subfolder1").mkdir()
    (test_dir / "subfolder2").mkdir()
    (test_dir / "subfolder1" / "deep").mkdir()
    
    # 创建测试文件
    test_files = [
        "video1.mp4",
        "movie.mp4",
        "subfolder1/video2.mp4",
        "subfolder1/deep/video3.mp4",
        "subfolder2/video4.mp4",
        "not_video.txt",  # 非mp4文件，应该被忽略
        "another.avi"     # 非mp4文件，应该被忽略
    ]
    
    for file_path in test_files:
        full_path = test_dir / file_path
        # 创建空文件
        full_path.touch()
        print(f"创建测试文件: {full_path}")
    
    return test_dir


def test_dry_run():
    """测试预览模式"""
    print("\n" + "="*60)
    print("测试 1: 预览模式")
    print("="*60)
    
    test_dir = create_test_files()
    
    try:
        success, error, errors = rename_mp4_to_ts(str(test_dir), dry_run=True)
        print(f"\n预览模式结果: 成功={success}, 错误={error}")
        
        # 验证文件没有被实际重命名
        mp4_files = list(test_dir.rglob("*.mp4"))
        ts_files = list(test_dir.rglob("*.ts"))
        
        print(f"预览后仍有 {len(mp4_files)} 个 .mp4 文件")
        print(f"预览后有 {len(ts_files)} 个 .ts 文件")
        
        assert len(mp4_files) == 5, f"应该有5个mp4文件，实际有{len(mp4_files)}个"
        assert len(ts_files) == 0, f"预览模式不应该创建ts文件，实际有{len(ts_files)}个"
        
        print("✅ 预览模式测试通过")
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)


def test_actual_rename():
    """测试实际重命名"""
    print("\n" + "="*60)
    print("测试 2: 实际重命名")
    print("="*60)
    
    test_dir = create_test_files()
    
    try:
        success, error, errors = rename_mp4_to_ts(str(test_dir), dry_run=False)
        print(f"\n实际重命名结果: 成功={success}, 错误={error}")
        
        # 验证文件被正确重命名
        mp4_files = list(test_dir.rglob("*.mp4"))
        ts_files = list(test_dir.rglob("*.ts"))
        
        print(f"重命名后有 {len(mp4_files)} 个 .mp4 文件")
        print(f"重命名后有 {len(ts_files)} 个 .ts 文件")
        
        assert len(mp4_files) == 0, f"重命名后不应该有mp4文件，实际有{len(mp4_files)}个"
        assert len(ts_files) == 5, f"应该有5个ts文件，实际有{len(ts_files)}个"
        
        # 验证特定文件存在
        expected_ts_files = [
            "video1.ts",
            "movie.ts",
            "subfolder1/video2.ts",
            "subfolder1/deep/video3.ts",
            "subfolder2/video4.ts"
        ]
        
        for ts_file in expected_ts_files:
            ts_path = test_dir / ts_file
            assert ts_path.exists(), f"期望的ts文件不存在: {ts_path}"
        
        print("✅ 实际重命名测试通过")
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)


def test_conflict_handling():
    """测试文件冲突处理"""
    print("\n" + "="*60)
    print("测试 3: 文件冲突处理")
    print("="*60)
    
    test_dir = create_test_files()
    
    try:
        # 先创建一个同名的 .ts 文件
        conflict_ts = test_dir / "video1.ts"
        conflict_ts.touch()
        print(f"创建冲突文件: {conflict_ts}")
        
        success, error, errors = rename_mp4_to_ts(str(test_dir), dry_run=False)
        print(f"\n冲突处理结果: 成功={success}, 错误={error}")
        
        # 验证冲突文件被跳过
        mp4_files = list(test_dir.rglob("*.mp4"))
        ts_files = list(test_dir.rglob("*.ts"))
        
        print(f"处理后有 {len(mp4_files)} 个 .mp4 文件")
        print(f"处理后有 {len(ts_files)} 个 .ts 文件")
        
        # video1.mp4 应该因为冲突而没有被重命名
        video1_mp4 = test_dir / "video1.mp4"
        assert video1_mp4.exists(), "video1.mp4应该因为冲突而保留"
        
        # 其他文件应该被正常重命名
        assert len(mp4_files) == 1, f"应该有1个mp4文件因冲突保留，实际有{len(mp4_files)}个"
        assert len(ts_files) == 5, f"应该有5个ts文件，实际有{len(ts_files)}个"
        
        print("✅ 文件冲突处理测试通过")
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)


def test_nonexistent_directory():
    """测试不存在的目录"""
    print("\n" + "="*60)
    print("测试 4: 不存在的目录")
    print("="*60)
    
    nonexistent_dir = "/this/directory/does/not/exist"
    success, error, errors = rename_mp4_to_ts(nonexistent_dir, dry_run=True)
    
    print(f"不存在目录测试结果: 成功={success}, 错误={error}")
    assert error == 1, "应该返回错误"
    assert success == 0, "不应该有成功的操作"
    
    print("✅ 不存在目录测试通过")


def main():
    """运行所有测试"""
    print("开始运行 MP4 to TS 重命名功能测试")
    
    try:
        test_dry_run()
        test_actual_rename()
        test_conflict_handling()
        test_nonexistent_directory()
        
        print("\n" + "="*60)
        print("🎉 所有测试都通过了！")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
