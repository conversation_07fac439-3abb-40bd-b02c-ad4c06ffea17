#!/usr/bin/env python3
"""
演示断点续传功能
创建测试文件和模拟上传场景
"""

import os
import tempfile
import shutil
from pathlib import Path

def create_demo_structure():
    """创建演示用的目录结构和MP4文件"""
    
    # 创建临时目录
    demo_dir = Path(tempfile.mkdtemp(prefix="oss_upload_demo_"))
    
    # 创建目录结构
    structure = [
        "人教/七年级/上册/1.1正数和负数",
        "人教/七年级/上册/1.2有理数",
        "人教/八年级/上册/11.1与三角形有关的线段",
        "人教/八年级/下册/16.1二次根式"
    ]
    
    print(f"📁 创建演示目录: {demo_dir}")
    print("📂 创建目录结构:")
    
    for path in structure:
        full_path = demo_dir / path
        full_path.mkdir(parents=True, exist_ok=True)
        print(f"   📁 {path}")
        
        # 在每个目录中创建1-2个MP4文件（大于1MB）
        for i in range(1, 3):
            mp4_file = full_path / f"lesson_{i}.mp4"
            # 创建大于1MB的文件内容（模拟真实MP4）
            content = f"这是 {path} 的第 {i} 个视频文件\n" * (50000 * i)  # 增大文件
            mp4_file.write_bytes(content.encode('utf-8'))
            size_mb = len(content) / (1024 * 1024)
            print(f"      📹 {mp4_file.name} ({size_mb:.1f} MB)")
    
    return demo_dir

def demo_first_upload(demo_dir):
    """演示第一次上传（预览模式）"""
    print("\n" + "=" * 80)
    print("🚀 第一次上传演示（预览模式）")
    print("=" * 80)
    
    cmd = f"python upload_mp4_to_oss.py '{demo_dir}' --dry-run --csv-file demo_uploads.csv"
    print(f"执行命令: {cmd}")
    print()
    
    os.system(cmd)

def create_partial_upload_history(demo_dir):
    """创建部分上传历史，模拟中断场景"""
    print("\n" + "=" * 80)
    print("📝 模拟部分上传历史")
    print("=" * 80)
    
    from upload_mp4_to_oss import find_mp4_files, save_upload_record, calculate_file_hash
    from datetime import datetime
    import uuid
    
    # 找到所有MP4文件
    mp4_files = find_mp4_files(demo_dir)
    csv_file = Path("demo_uploads.csv")
    
    # 模拟前一半文件已上传成功
    uploaded_count = len(mp4_files) // 2
    
    print(f"📊 总文件数: {len(mp4_files)}")
    print(f"✅ 模拟已上传: {uploaded_count} 个文件")
    print(f"⏳ 待上传: {len(mp4_files) - uploaded_count} 个文件")
    print()
    
    for i, mp4_file in enumerate(mp4_files[:uploaded_count]):
        file_size_mb = mp4_file.stat().st_size / (1024 * 1024)
        file_hash = calculate_file_hash(mp4_file)
        
        record = {
            'uuid': str(uuid.uuid4()),
            'filename': mp4_file.name,
            'oss_key': f"video/demo/{mp4_file.relative_to(demo_dir)}".replace('\\', '/'),
            'file_size_mb': f"{file_size_mb:.2f}",
            'file_hash': file_hash,
            'file_path': str(mp4_file),
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'completed'
        }
        
        save_upload_record(csv_file, record)
        print(f"✅ 记录已上传: {mp4_file.name}")
    
    print(f"\n📄 上传历史已保存到: {csv_file}")

def demo_resume_upload(demo_dir):
    """演示断点续传"""
    print("\n" + "=" * 80)
    print("🔄 断点续传演示")
    print("=" * 80)
    
    cmd = f"python upload_mp4_to_oss.py '{demo_dir}' --dry-run --csv-file demo_uploads.csv"
    print(f"执行命令: {cmd}")
    print("注意：程序会自动跳过已上传的文件")
    print()
    
    os.system(cmd)

def demo_no_resume_upload(demo_dir):
    """演示禁用断点续传"""
    print("\n" + "=" * 80)
    print("🚫 禁用断点续传演示")
    print("=" * 80)
    
    cmd = f"python upload_mp4_to_oss.py '{demo_dir}' --dry-run --csv-file demo_uploads.csv --no-resume"
    print(f"执行命令: {cmd}")
    print("注意：程序会重新上传所有文件")
    print()
    
    os.system(cmd)

def show_csv_content():
    """显示CSV文件内容"""
    print("\n" + "=" * 80)
    print("📄 查看上传历史记录")
    print("=" * 80)
    
    csv_file = Path("demo_uploads.csv")
    if csv_file.exists():
        with open(csv_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    else:
        print("❌ CSV文件不存在")

def cleanup_demo(demo_dir):
    """清理演示文件"""
    print("\n" + "=" * 80)
    print("🧹 清理演示文件")
    print("=" * 80)
    
    # 删除演示目录
    if demo_dir.exists():
        shutil.rmtree(demo_dir)
        print(f"🗑️  删除演示目录: {demo_dir}")
    
    # 删除CSV文件
    csv_file = Path("demo_uploads.csv")
    if csv_file.exists():
        csv_file.unlink()
        print(f"🗑️  删除CSV文件: {csv_file}")

def main():
    """运行完整演示"""
    print("🎬 OSS上传断点续传功能演示")
    print("=" * 80)
    
    try:
        # 1. 创建演示结构
        demo_dir = create_demo_structure()
        
        # 2. 第一次上传（预览）
        demo_first_upload(demo_dir)
        
        # 3. 创建部分上传历史
        create_partial_upload_history(demo_dir)
        
        # 4. 显示CSV内容
        show_csv_content()
        
        # 5. 演示断点续传
        demo_resume_upload(demo_dir)
        
        # 6. 演示禁用断点续传
        demo_no_resume_upload(demo_dir)
        
        print("\n" + "=" * 80)
        print("✅ 演示完成！")
        print("=" * 80)
        print("📋 演示要点:")
        print("1. 程序会自动检测已上传的文件")
        print("2. 通过文件哈希值验证文件完整性")
        print("3. 只上传新增或更改的文件")
        print("4. 可以通过 --no-resume 禁用断点续传")
        print("5. 所有操作都会记录在CSV文件中")
        
        # 询问是否清理
        response = input("\n是否清理演示文件？(y/N): ").strip().lower()
        if response in ['y', 'yes']:
            cleanup_demo(demo_dir)
        else:
            print(f"📁 演示文件保留在: {demo_dir}")
            print(f"📄 CSV文件: demo_uploads.csv")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
