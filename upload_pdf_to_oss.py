#!/usr/bin/env python3
"""
PDF文件上传到阿里云OSS工具

功能：
1. 扫描指定目录（包含子目录）下所有PDF格式电子书
2. 上传到阿里云OSS
3. OSS key规则：视频所在目录往上拼接2级，最前边拼接"pdf"
4. 生成UUID，记录到CSV文件

使用方法：
python upload_pdf_to_oss.py /path/to/pdf/directory

环境变量配置：
export OSS_ACCESS_KEY_ID="your_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_access_key_secret"
export OSS_ENDPOINT="your_endpoint"
export OSS_BUCKET_NAME="your_bucket_name"
"""

import os
import sys
import csv
import uuid
import hashlib
from pathlib import Path
from typing import List, Dict, Optional
import argparse
from datetime import datetime

try:
    import oss2
except ImportError:
    print("❌ 请安装阿里云OSS SDK: pip install oss2")
    sys.exit(1)

class PDFOSSUploader:
    def __init__(self):
        """初始化OSS上传器"""
        self.access_key_id = os.getenv('OSS_ACCESS_KEY_ID')
        self.access_key_secret = os.getenv('OSS_ACCESS_KEY_SECRET')
        self.endpoint = os.getenv('OSS_ENDPOINT')
        self.bucket_name = os.getenv('OSS_BUCKET_NAME')
        
        if not all([self.access_key_id, self.access_key_secret, self.endpoint, self.bucket_name]):
            print("❌ 请设置环境变量:")
            print("   export OSS_ACCESS_KEY_ID='your_access_key_id'")
            print("   export OSS_ACCESS_KEY_SECRET='your_access_key_secret'")
            print("   export OSS_ENDPOINT='your_endpoint'")
            print("   export OSS_BUCKET_NAME='your_bucket_name'")
            sys.exit(1)
        
        # 初始化OSS客户端
        try:
            auth = oss2.Auth(self.access_key_id, self.access_key_secret)
            self.bucket = oss2.Bucket(auth, self.endpoint, self.bucket_name)
            print(f"✅ OSS连接成功: {self.bucket_name}")
        except Exception as e:
            print(f"❌ OSS连接失败: {e}")
            sys.exit(1)

    def scan_pdf_files(self, directory: Path) -> List[Path]:
        """
        扫描目录下所有PDF文件
        
        Args:
            directory: 要扫描的目录路径
            
        Returns:
            List[Path]: PDF文件路径列表
        """
        pdf_files = []
        
        print(f"🔍 扫描目录: {directory}")
        
        try:
            # 递归查找所有PDF文件
            for pdf_file in directory.rglob("*.pdf"):
                if pdf_file.is_file():
                    # 排除系统文件和隐藏文件
                    if not pdf_file.name.startswith('.') and pdf_file.stat().st_size > 0:
                        pdf_files.append(pdf_file)
            
            print(f"📚 找到 {len(pdf_files)} 个PDF文件")
            return pdf_files
            
        except Exception as e:
            print(f"❌ 扫描目录失败: {e}")
            return []

    def generate_oss_key(self, pdf_path: Path, base_directory: Path) -> str:
        """
        生成OSS key
        
        规则：视频所在目录往上拼接2级，最前边拼接"pdf"
        
        Args:
            pdf_path: PDF文件路径
            base_directory: 基础目录路径
            
        Returns:
            str: OSS key
        """
        try:
            # 获取相对于基础目录的路径
            relative_path = pdf_path.relative_to(base_directory)
            
            # 获取目录部分（不包含文件名）
            dir_parts = relative_path.parent.parts
            
            # 取最后2级目录
            if len(dir_parts) >= 2:
                key_parts = dir_parts[-2:]
            elif len(dir_parts) == 1:
                key_parts = dir_parts
            else:
                key_parts = ("root",)
            
            # 拼接OSS key: pdf + 目录层级 + 文件名
            oss_key = "pdf/" + "/".join(key_parts) + "/" + pdf_path.name
            
            return oss_key
            
        except Exception as e:
            print(f"❌ 生成OSS key失败: {e}")
            # fallback方案
            return f"pdf/unknown/{pdf_path.name}"

    def calculate_file_hash(self, file_path: Path) -> str:
        """计算文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            print(f"❌ 计算文件哈希失败: {e}")
            return ""

    def upload_file(self, file_path: Path, oss_key: str) -> bool:
        """
        上传文件到OSS
        
        Args:
            file_path: 本地文件路径
            oss_key: OSS对象键
            
        Returns:
            bool: 上传是否成功
        """
        try:
            # 设置上传参数
            headers = {
                'Content-Type': 'application/pdf',
                'x-oss-object-acl': 'private'  # 设置为私有访问
            }
            
            # 上传文件
            with open(file_path, 'rb') as f:
                result = self.bucket.put_object(oss_key, f, headers=headers)
            
            if result.status == 200:
                print(f"✅ 上传成功: {oss_key}")
                return True
            else:
                print(f"❌ 上传失败: {oss_key}, 状态码: {result.status}")
                return False
                
        except Exception as e:
            print(f"❌ 上传文件失败: {e}")
            return False

    def save_to_csv(self, records: List[Dict], csv_path: Path):
        """
        保存记录到CSV文件
        
        Args:
            records: 记录列表
            csv_path: CSV文件路径
        """
        try:
            # 检查文件是否存在，如果不存在则创建表头
            file_exists = csv_path.exists()
            
            with open(csv_path, 'a', newline='', encoding='utf-8') as f:
                fieldnames = ['uuid', 'filename', 'oss_key', 'file_size_mb', 'file_hash', 
                             'file_path', 'upload_time', 'status']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                # 如果文件不存在，写入表头
                if not file_exists:
                    writer.writeheader()
                
                # 写入记录
                for record in records:
                    writer.writerow(record)
            
            print(f"📄 记录已保存到: {csv_path}")
            
        except Exception as e:
            print(f"❌ 保存CSV失败: {e}")

    def process_directory(self, directory: Path, csv_path: Path = None):
        """
        处理目录中的所有PDF文件
        
        Args:
            directory: 要处理的目录
            csv_path: CSV输出文件路径
        """
        if csv_path is None:
            csv_path = Path("pdf_oss_files.csv")
        
        print("🚀 开始处理PDF文件上传")
        print("=" * 60)
        
        # 扫描PDF文件
        pdf_files = self.scan_pdf_files(directory)
        
        if not pdf_files:
            print("❌ 没有找到PDF文件")
            return
        
        # 处理每个PDF文件
        records = []
        success_count = 0
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"\n📚 处理文件 {i}/{len(pdf_files)}: {pdf_file.name}")
            
            try:
                # 生成UUID
                file_uuid = str(uuid.uuid4())
                
                # 生成OSS key
                oss_key = self.generate_oss_key(pdf_file, directory)
                
                # 计算文件信息
                file_size_mb = round(pdf_file.stat().st_size / (1024 * 1024), 2)
                file_hash = self.calculate_file_hash(pdf_file)
                
                print(f"   UUID: {file_uuid}")
                print(f"   OSS Key: {oss_key}")
                print(f"   文件大小: {file_size_mb} MB")
                
                # 上传文件
                upload_success = self.upload_file(pdf_file, oss_key)
                
                # 创建记录
                record = {
                    'uuid': file_uuid,
                    'filename': pdf_file.name,
                    'oss_key': oss_key,
                    'file_size_mb': file_size_mb,
                    'file_hash': file_hash,
                    'file_path': str(pdf_file.absolute()),
                    'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'status': 'completed' if upload_success else 'failed'
                }
                
                records.append(record)
                
                if upload_success:
                    success_count += 1
                
            except Exception as e:
                print(f"❌ 处理文件失败: {e}")
                # 添加失败记录
                record = {
                    'uuid': str(uuid.uuid4()),
                    'filename': pdf_file.name,
                    'oss_key': 'failed',
                    'file_size_mb': 0,
                    'file_hash': '',
                    'file_path': str(pdf_file.absolute()),
                    'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'status': 'error'
                }
                records.append(record)
        
        # 保存记录到CSV
        if records:
            self.save_to_csv(records, csv_path)
        
        # 打印统计信息
        print("\n" + "=" * 60)
        print("📊 上传统计:")
        print(f"   总文件数: {len(pdf_files)}")
        print(f"   成功上传: {success_count}")
        print(f"   失败数量: {len(pdf_files) - success_count}")
        print(f"   成功率: {success_count/len(pdf_files)*100:.1f}%")
        print(f"   CSV文件: {csv_path}")
        print("✅ 处理完成!")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PDF文件上传到阿里云OSS工具')
    parser.add_argument('directory', help='要扫描的PDF文件目录')
    parser.add_argument('--csv', '-c', help='CSV输出文件路径', default='pdf_oss_files.csv')
    parser.add_argument('--dry-run', '-d', action='store_true', help='预览模式，不实际上传')
    
    args = parser.parse_args()
    
    # 检查目录
    directory = Path(args.directory)
    if not directory.exists():
        print(f"❌ 目录不存在: {directory}")
        sys.exit(1)
    
    if not directory.is_dir():
        print(f"❌ 不是有效目录: {directory}")
        sys.exit(1)
    
    print("📚 PDF文件上传到阿里云OSS工具")
    print("=" * 50)
    print(f"📂 扫描目录: {directory}")
    print(f"📄 CSV文件: {args.csv}")
    
    if args.dry_run:
        print("🔍 预览模式 - 不会实际上传文件")
        # TODO: 实现预览模式
        return
    
    try:
        # 创建上传器并处理
        uploader = PDFOSSUploader()
        uploader.process_directory(directory, Path(args.csv))
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
