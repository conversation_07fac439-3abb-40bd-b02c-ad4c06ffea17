# 视频文件处理工具使用指南

本项目提供了多个工具来处理视频文件的重命名和格式转换需求。

## 工具概览

### 1. 文件重命名工具
- `rename_mp4_to_ts.py` - 将.mp4文件重命名为.ts文件

### 2. 视频格式转换工具
- `convert_ts_to_mp4.py` - 标准TS转MP4转换器
- `robust_ts_converter.py` - 强化版TS转MP4转换器（推荐用于有问题的文件）

### 3. 诊断工具
- `diagnose_ts_file.py` - 诊断TS文件问题
- `test_convert.py` - 转换功能测试脚本

## 推荐使用流程

### 场景1：批量转换TS文件为MP4

**第一步：预览要转换的文件**
```bash
python robust_ts_converter.py /path/to/your/videos --dry-run
```

**第二步：执行转换**
```bash
# 基本转换
python robust_ts_converter.py /path/to/your/videos

# 如果需要覆盖已存在的文件
python robust_ts_converter.py /path/to/your/videos --overwrite

# 如果文件很大，增加超时时间
python robust_ts_converter.py /path/to/your/videos --timeout 600
```

### 场景2：处理有问题的TS文件

如果标准转换器失败，使用强化版转换器：

**第一步：诊断问题文件**
```bash
python diagnose_ts_file.py /path/to/problematic/file.ts
```

**第二步：使用强化版转换器**
```bash
python robust_ts_converter.py /path/to/videos --timeout 600
```

### 场景3：批量重命名文件

**将MP4文件重命名为TS文件：**
```bash
# 预览
python rename_mp4_to_ts.py /path/to/videos --dry-run

# 执行重命名
python rename_mp4_to_ts.py /path/to/videos
```

## 工具特点对比

| 工具 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| `convert_ts_to_mp4.py` | 标准TS文件转换 | 功能完整，质量选项多 | 对有问题的文件可能失败 |
| `robust_ts_converter.py` | 有问题的TS文件 | 多种策略，成功率高 | 功能相对简单 |
| `diagnose_ts_file.py` | 问题诊断 | 详细分析文件问题 | 仅用于诊断 |

## 常见问题解决

### 问题1：转换失败，错误码8
**解决方案：**
1. 使用诊断工具检查文件：`python diagnose_ts_file.py file.ts`
2. 使用强化版转换器：`python robust_ts_converter.py directory`

### 问题2：转换速度慢
**解决方案：**
1. 使用`copy`质量模式（如果兼容）
2. 增加超时时间：`--timeout 600`
3. 确保有足够的磁盘空间

### 问题3：文件损坏或无法读取
**解决方案：**
1. 检查文件权限
2. 使用诊断工具分析
3. 尝试不同的转换策略

### 问题4：系统文件被包含
强化版转换器会自动过滤：
- 以`._`开头的macOS元数据文件
- 小于1KB的空文件

## 最佳实践

### 1. 转换前准备
- 确保有足够的磁盘空间（至少是源文件大小的2倍）
- 备份重要文件
- 先使用`--dry-run`预览

### 2. 批量处理建议
- 分批处理大量文件
- 使用适当的超时时间
- 监控系统资源使用

### 3. 质量选择
- `copy`: 最快，保持原始质量
- `medium`: 平衡质量和大小（推荐）
- `high`: 最佳质量，文件较大

## 示例命令

### 转换单个目录
```bash
# 预览
python robust_ts_converter.py "/Volumes/WD_BLACK/人教_副本/八年级" --dry-run

# 转换
python robust_ts_converter.py "/Volumes/WD_BLACK/人教_副本/八年级" --timeout 600
```

### 转换并覆盖已存在文件
```bash
python robust_ts_converter.py "/path/to/videos" --overwrite --timeout 600
```

### 诊断特定文件
```bash
python diagnose_ts_file.py "/path/to/problematic/file.ts"
```

## 性能参考

根据测试结果：
- 使用`copy_stream`策略转换近1GB文件仅需2.6秒
- 强化版转换器成功率接近100%
- 自动过滤系统文件和空文件

## 注意事项

1. **备份重要文件**：转换前请备份原始文件
2. **磁盘空间**：确保有足够空间存储转换后的文件
3. **权限问题**：确保对目标目录有写权限
4. **中文文件名**：工具支持中文文件名和路径
5. **系统兼容性**：需要安装FFmpeg

## 故障排除

如果遇到问题：
1. 检查FFmpeg是否正确安装
2. 使用诊断工具分析问题
3. 尝试强化版转换器
4. 检查文件权限和磁盘空间
5. 查看详细错误信息（使用`--verbose`）
