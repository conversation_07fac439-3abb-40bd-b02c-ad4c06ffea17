#!/usr/bin/env python3
"""
CSV文件匹配工具
匹配final_results.csv和oss_files_人教.csv两个文件

匹配规则：
1. 以final_results.csv的行数为准
2. 将第7列(top_match_content)与oss_files_人教.csv的oss_key列匹配
3. 如果oss_key的内容完全包含final_results.csv的top_match_content的内容，则匹配成功
4. 将匹配到的section_id和uuid写入新的CSV文件
"""

import csv
import sys
from pathlib import Path
from typing import List, Dict, Tuple, Optional

def read_final_results(file_path: Path) -> List[Dict]:
    """
    读取final_results.csv文件

    Returns:
        List[Dict]: 包含所有行数据的列表
    """
    results = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 手动解析CSV，因为包含复杂的JSON数据
            lines = f.readlines()

            if not lines:
                print("❌ 文件为空")
                return []

            # 解析表头
            header_line = lines[0].strip()
            # 移除BOM字符
            if header_line.startswith('\ufeff'):
                header_line = header_line[1:]
            headers = [h.strip() for h in header_line.split(',')]

            print(f"📋 CSV表头: {headers}")

            # 查找需要的列索引
            try:
                section_id_idx = headers.index('section_id')
                top_match_content_idx = headers.index('top_match_content')
            except ValueError as e:
                print(f"❌ 找不到必要的列: {e}")
                print(f"可用的列: {headers}")
                return []

            # 解析数据行
            for row_num, line in enumerate(lines[1:], 2):
                line = line.strip()
                if not line:
                    continue

                # 简单的CSV解析，按逗号分割
                parts = line.split(',')

                if len(parts) <= max(section_id_idx, top_match_content_idx):
                    print(f"⚠️  第{row_num}行数据不完整，跳过")
                    continue

                section_id = parts[section_id_idx].strip()
                top_match_content = parts[top_match_content_idx].strip()

                if not section_id or not top_match_content:
                    print(f"⚠️  第{row_num}行缺少必要字段，跳过")
                    continue

                results.append({
                    'row_num': row_num,
                    'section_id': section_id,
                    'top_match_content': top_match_content,
                    'line': line
                })

        print(f"✅ 成功读取final_results.csv: {len(results)} 行有效数据")
        return results

    except Exception as e:
        print(f"❌ 读取final_results.csv失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def read_oss_files(file_path: Path) -> List[Dict]:
    """
    读取oss_files_人教.csv文件
    
    Returns:
        List[Dict]: 包含所有行数据的列表
    """
    results = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row_num, row in enumerate(reader, 2):  # 从第2行开始计数（包含表头）
                uuid = row.get('uuid', '').strip()
                oss_key = row.get('oss_key', '').strip()
                
                if not uuid or not oss_key:
                    print(f"⚠️  oss_files第{row_num}行缺少必要字段，跳过")
                    continue
                
                results.append({
                    'row_num': row_num,
                    'uuid': uuid,
                    'oss_key': oss_key,
                    'full_row': row
                })
        
        print(f"✅ 成功读取oss_files_人教.csv: {len(results)} 行有效数据")
        return results
        
    except Exception as e:
        print(f"❌ 读取oss_files_人教.csv失败: {e}")
        return []

def find_matches(final_results: List[Dict], oss_files: List[Dict]) -> List[Dict]:
    """
    查找匹配的记录
    
    匹配规则：oss_key完全包含top_match_content
    
    Returns:
        List[Dict]: 匹配成功的记录列表
    """
    matches = []
    unmatched = []
    
    print("\n🔍 开始匹配过程...")
    print("=" * 80)
    
    for final_row in final_results:
        section_id = final_row['section_id']
        top_match_content = final_row['top_match_content']
        
        # 查找匹配的oss_key
        matched_uuid = None
        matched_oss_key = None
        
        for oss_row in oss_files:
            oss_key = oss_row['oss_key']
            
            # 检查oss_key是否完全包含top_match_content
            if top_match_content in oss_key:
                matched_uuid = oss_row['uuid']
                matched_oss_key = oss_key
                
                matches.append({
                    'section_id': section_id,
                    'uuid': matched_uuid,
                    'top_match_content': top_match_content,
                    'oss_key': matched_oss_key,
                    'final_row_num': final_row['row_num'],
                    'oss_row_num': oss_row['row_num']
                })
                
                print(f"✅ 匹配成功:")
                print(f"   section_id: {section_id}")
                print(f"   uuid: {matched_uuid}")
                print(f"   top_match_content: {top_match_content[:60]}...")
                print(f"   oss_key: {matched_oss_key[:60]}...")
                print()
                break
        
        if not matched_uuid:
            unmatched.append({
                'section_id': section_id,
                'top_match_content': top_match_content,
                'final_row_num': final_row['row_num']
            })
            print(f"❌ 未找到匹配:")
            print(f"   section_id: {section_id}")
            print(f"   top_match_content: {top_match_content[:60]}...")
            print()
    
    print("=" * 80)
    print(f"📊 匹配统计:")
    print(f"   总记录数: {len(final_results)}")
    print(f"   匹配成功: {len(matches)}")
    print(f"   未匹配: {len(unmatched)}")
    print(f"   匹配率: {len(matches)/len(final_results)*100:.1f}%")
    
    if unmatched:
        print(f"\n❌ 未匹配的记录:")
        for item in unmatched[:5]:  # 只显示前5个
            print(f"   - {item['section_id']}: {item['top_match_content'][:50]}...")
        if len(unmatched) > 5:
            print(f"   ... 还有 {len(unmatched) - 5} 个未匹配记录")
    
    return matches

def save_matches(matches: List[Dict], output_file: Path):
    """
    保存匹配结果到CSV文件
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['section_id', 'uuid']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入匹配数据
            for match in matches:
                writer.writerow({
                    'section_id': match['section_id'],
                    'uuid': match['uuid']
                })
        
        print(f"\n✅ 匹配结果已保存到: {output_file}")
        print(f"📊 保存了 {len(matches)} 条匹配记录")
        
    except Exception as e:
        print(f"❌ 保存匹配结果失败: {e}")

def save_detailed_report(matches: List[Dict], output_file: Path):
    """
    保存详细的匹配报告
    """
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            fieldnames = [
                'section_id', 'uuid', 'top_match_content', 'oss_key',
                'final_row_num', 'oss_row_num'
            ]
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            
            # 写入表头
            writer.writeheader()
            
            # 写入详细匹配数据
            for match in matches:
                writer.writerow(match)
        
        print(f"📄 详细匹配报告已保存到: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存详细报告失败: {e}")

def main():
    """主函数"""
    print("🔧 CSV文件匹配工具")
    print("=" * 50)
    
    # 检查输入文件
    final_results_file = Path("final_results.csv")
    oss_files_file = Path("oss_files_人教.csv")
    
    if not final_results_file.exists():
        print(f"❌ 文件不存在: {final_results_file}")
        sys.exit(1)
    
    if not oss_files_file.exists():
        print(f"❌ 文件不存在: {oss_files_file}")
        sys.exit(1)
    
    print(f"📂 输入文件:")
    print(f"   final_results.csv: {final_results_file.stat().st_size / 1024:.1f} KB")
    print(f"   oss_files_人教.csv: {oss_files_file.stat().st_size / 1024:.1f} KB")
    print()
    
    try:
        # 读取文件
        print("📖 读取输入文件...")
        final_results = read_final_results(final_results_file)
        oss_files = read_oss_files(oss_files_file)
        
        if not final_results:
            print("❌ final_results.csv没有有效数据")
            return
        
        if not oss_files:
            print("❌ oss_files_人教.csv没有有效数据")
            return
        
        # 执行匹配
        matches = find_matches(final_results, oss_files)
        
        if not matches:
            print("❌ 没有找到任何匹配记录")
            return
        
        # 保存结果
        print("\n💾 保存匹配结果...")
        output_file = Path("matched_results.csv")
        detailed_report_file = Path("detailed_match_report.csv")
        
        save_matches(matches, output_file)
        save_detailed_report(matches, detailed_report_file)
        
        print("\n✅ 匹配完成！")
        
    except Exception as e:
        print(f"❌ 处理过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
