#!/usr/bin/env python3
"""
简单TS转MP4转换器

使用 ffmpeg -i input.ts -c:v copy -c:a copy output.mp4 策略
直接复制视频和音频流，不重新编码，速度最快
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
import time


def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        return result.returncode == 0
    except:
        return False


def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = file_path.stat().st_size
        return size_bytes / (1024 * 1024)
    except:
        return 0


def convert_single_file(input_file, output_file, timeout=300):
    """
    转换单个TS文件为MP4
    
    Args:
        input_file (Path): 输入TS文件
        output_file (Path): 输出MP4文件
        timeout (int): 转换超时时间（秒）
    
    Returns:
        tuple: (success, error_message, elapsed_time)
    """
    try:
        # 构建FFmpeg命令：ffmpeg -i input.ts -c:v copy -c:a copy output.mp4
        cmd = [
            'ffmpeg',
            '-i', str(input_file),
            '-c:v', 'copy',
            '-c:a', 'copy',
            '-y',  # 覆盖输出文件
            str(output_file)
        ]
        
        start_time = time.time()
        
        # 执行转换
        result = subprocess.run(cmd, 
                              capture_output=True, 
                              text=True, 
                              timeout=timeout)
        
        elapsed_time = time.time() - start_time
        
        if result.returncode == 0:
            return True, None, elapsed_time
        else:
            error_msg = f"FFmpeg返回码: {result.returncode}"
            if result.stderr:
                # 提取关键错误信息
                stderr_lines = result.stderr.split('\n')
                error_lines = [line.strip() for line in stderr_lines 
                             if any(keyword in line.lower() 
                                   for keyword in ['error', 'failed', 'invalid', 'corrupt'])]
                if error_lines:
                    error_msg += f" - {error_lines[-1][:150]}"
            return False, error_msg, elapsed_time
            
    except subprocess.TimeoutExpired:
        return False, f"转换超时（{timeout}秒）", timeout
    except Exception as e:
        return False, f"转换异常: {str(e)}", 0


def convert_ts_directory(directory_path, dry_run=False, overwrite=False, 
                        timeout=300, add_suffix=False):
    """
    转换目录下所有TS文件为MP4
    
    Args:
        directory_path (str): 目录路径
        dry_run (bool): 预览模式
        overwrite (bool): 覆盖已存在文件
        timeout (int): 转换超时时间
        add_suffix (bool): 是否在输出文件名添加后缀
    
    Returns:
        tuple: (success_count, error_count, errors)
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"❌ 错误: 目录 '{directory_path}' 不存在")
        return 0, 1, [f"目录 '{directory_path}' 不存在"]
    
    if not directory.is_dir():
        print(f"❌ 错误: '{directory_path}' 不是一个目录")
        return 0, 1, [f"'{directory_path}' 不是一个目录"]
    
    if not check_ffmpeg():
        error_msg = "❌ 错误: 系统中未找到ffmpeg，请先安装ffmpeg"
        print(error_msg)
        print("安装方法:")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  Windows: 从 https://ffmpeg.org/download.html 下载")
        return 0, 1, [error_msg]
    
    # 查找所有TS文件，过滤系统文件
    all_ts_files = list(directory.rglob("*.ts"))
    ts_files = []
    
    for ts_file in all_ts_files:
        # 跳过macOS元数据文件
        if ts_file.name.startswith('._'):
            continue
        
        # 跳过空文件或过小的文件（小于1KB）
        try:
            if ts_file.stat().st_size < 1024:
                continue
        except:
            continue
        
        ts_files.append(ts_file)
    
    if not ts_files:
        print(f"❌ 在目录 '{directory_path}' 中没有找到任何有效的 .ts 文件")
        if all_ts_files:
            print(f"发现 {len(all_ts_files)} 个 .ts 文件，但都被过滤掉了（系统文件或空文件）")
        return 0, 0, []
    
    print(f"📁 找到 {len(ts_files)} 个有效的 .ts 文件")
    
    # 计算总文件大小
    total_size_mb = sum(get_file_size_mb(ts_file) for ts_file in ts_files)
    print(f"📊 总文件大小: {total_size_mb:.1f} MB")
    print(f"⚙️  转换策略: 直接复制流 (-c:v copy -c:a copy)")
    print(f"⏱️  超时设置: {timeout} 秒/文件")
    print("-" * 60)
    
    success_count = 0
    error_count = 0
    errors = []
    processed_size_mb = 0
    
    for i, ts_file in enumerate(ts_files, 1):
        file_size_mb = get_file_size_mb(ts_file)
        
        # 构造输出文件名
        if add_suffix:
            # 添加后缀，如：video.ts -> video_converted.mp4
            output_name = f"{ts_file.stem}_converted.mp4"
        else:
            # 直接替换扩展名，如：video.ts -> video.mp4
            output_name = f"{ts_file.stem}.mp4"
        
        output_file = ts_file.parent / output_name
        
        print(f"[{i}/{len(ts_files)}] 📹 {ts_file.name} ({file_size_mb:.1f} MB)")
        
        if dry_run:
            print(f"  [预览] {ts_file.name} -> {output_name}")
            success_count += 1
            processed_size_mb += file_size_mb
        else:
            # 检查目标文件是否存在
            if output_file.exists() and not overwrite:
                print(f"  ⏭️  跳过: 目标文件已存在 ({output_name})")
                error_count += 1
                errors.append(f"目标文件已存在: {output_name}")
                continue
            
            print(f"  🔄 转换中: {ts_file.name} -> {output_name}")
            
            # 执行转换
            success, error_msg, elapsed_time = convert_single_file(
                ts_file, output_file, timeout
            )
            
            if success:
                output_size_mb = get_file_size_mb(output_file)
                size_change = ((output_size_mb - file_size_mb) / file_size_mb * 100) if file_size_mb > 0 else 0
                
                print(f"  ✅ 转换成功: {elapsed_time:.1f}s, "
                      f"输出: {output_size_mb:.1f} MB "
                      f"({size_change:+.1f}%)")
                
                success_count += 1
                processed_size_mb += file_size_mb
            else:
                print(f"  ❌ 转换失败: {error_msg}")
                errors.append(f"{ts_file.name}: {error_msg}")
                error_count += 1
        
        # 显示总体进度
        progress = (processed_size_mb / total_size_mb * 100) if total_size_mb > 0 else 0
        print(f"  📈 总进度: {progress:.1f}% ({processed_size_mb:.1f}/{total_size_mb:.1f} MB)")
        print()
    
    return success_count, error_count, errors


def main():
    parser = argparse.ArgumentParser(
        description="简单TS转MP4转换器 - 使用直接复制流策略",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
转换策略:
  使用 ffmpeg -i input.ts -c:v copy -c:a copy output.mp4
  直接复制视频和音频流，不重新编码，速度最快

示例:
  python simple_ts_to_mp4.py /path/to/videos
  python simple_ts_to_mp4.py /path/to/videos --dry-run
  python simple_ts_to_mp4.py /path/to/videos --overwrite
  python simple_ts_to_mp4.py /path/to/videos --add-suffix
  python simple_ts_to_mp4.py . --timeout 600

注意:
  - 此方法速度最快，但要求TS文件格式兼容MP4容器
  - 如果转换失败，可能需要使用重新编码的方法
        """
    )
    
    parser.add_argument(
        "directory",
        help="要处理的目录路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，只显示将要转换的文件，不实际执行转换"
    )
    
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="覆盖已存在的.mp4文件"
    )
    
    parser.add_argument(
        "--add-suffix",
        action="store_true",
        help="在输出文件名添加'_converted'后缀，避免文件名冲突"
    )
    
    parser.add_argument(
        "--timeout",
        type=int,
        default=300,
        help="每个文件的转换超时时间（秒，默认300）"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细错误信息"
    )
    
    args = parser.parse_args()
    
    # 转换为绝对路径
    directory_path = os.path.abspath(args.directory)
    
    print("=" * 60)
    print("🎬 简单TS转MP4转换器")
    print("=" * 60)
    print(f"📂 处理目录: {directory_path}")
    
    if args.dry_run:
        print("🔍 运行模式: 预览模式 (不会实际转换文件)")
    else:
        print("🚀 运行模式: 实际转换")
        if args.overwrite:
            print("⚠️  覆盖模式: 启用")
        if args.add_suffix:
            print("📝 文件命名: 添加'_converted'后缀")
    
    print("=" * 60)
    
    # 执行转换
    start_time = time.time()
    success_count, error_count, errors = convert_ts_directory(
        directory_path,
        dry_run=args.dry_run,
        overwrite=args.overwrite,
        timeout=args.timeout,
        add_suffix=args.add_suffix
    )
    total_time = time.time() - start_time
    
    print("=" * 60)
    print(f"🏁 处理完成 (总耗时: {total_time:.1f}秒):")
    print(f"  ✅ 成功: {success_count} 个文件")
    print(f"  ❌ 失败: {error_count} 个文件")
    
    if success_count > 0:
        avg_time = total_time / success_count if success_count > 0 else 0
        print(f"  ⚡ 平均转换速度: {avg_time:.1f}秒/文件")
    
    if errors:
        print(f"\n❌ 失败文件列表:")
        for i, error in enumerate(errors, 1):
            print(f"  {i}. {error}")
        
        if args.verbose:
            print(f"\n💡 建议:")
            print(f"  - 检查失败的文件是否损坏或格式不兼容")
            print(f"  - 尝试使用重新编码的转换器: robust_ts_converter.py")
            print(f"  - 使用诊断工具检查文件: diagnose_ts_file.py")
    
    # 返回适当的退出码
    sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
