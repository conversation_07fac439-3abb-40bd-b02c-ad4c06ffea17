#!/bin/bash
# 阿里云OSS配置示例
# 复制此文件为 oss_config.sh 并填入你的真实配置

# 阿里云AccessKey ID
export OSS_ACCESS_KEY_ID="LTAI5tH52JJv1MG3sH8eGWE2"

# 阿里云AccessKey Secret  
export OSS_ACCESS_KEY_SECRET="******************************"

# OSS Endpoint (根据你的bucket所在区域选择)
# 华东1（杭州）: https://oss-cn-hangzhou.aliyuncs.com
# 华东2（上海）: https://oss-cn-shanghai.aliyuncs.com  
# 华北1（青岛）: https://oss-cn-qingdao.aliyuncs.com
# 华北2（北京）: https://oss-cn-beijing.aliyuncs.com
# 华南1（深圳）: https://oss-cn-shenzhen.aliyuncs.com
export OSS_ENDPOINT="https://oss-cn-beijing.aliyuncs.com"

# OSS Bucket名称
export OSS_BUCKET_NAME="edu-knowledge-hub"

echo "OSS配置已加载:"
echo "  AccessKey ID: ${OSS_ACCESS_KEY_ID:0:8}..."
echo "  Endpoint: $OSS_ENDPOINT"
echo "  Bucket: $OSS_BUCKET_NAME"
