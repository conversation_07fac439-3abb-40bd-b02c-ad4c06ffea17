#!/usr/bin/env python3
"""
测试脚本：创建一些测试用的 .ts 文件，然后测试转换功能
"""

import os
import tempfile
import shutil
import subprocess
from pathlib import Path
from convert_ts_to_mp4 import convert_ts_to_mp4, check_ffmpeg


def create_test_ts_file(file_path, duration=5):
    """
    使用ffmpeg创建一个测试用的.ts文件
    
    Args:
        file_path (Path): 输出文件路径
        duration (int): 视频时长（秒）
    """
    try:
        # 创建一个简单的测试视频
        cmd = [
            'ffmpeg', '-f', 'lavfi', 
            '-i', f'testsrc=duration={duration}:size=320x240:rate=1',
            '-f', 'lavfi', 
            '-i', f'sine=frequency=1000:duration={duration}',
            '-c:v', 'libx264', '-c:a', 'aac',
            '-y', str(file_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        return result.returncode == 0
    except:
        return False


def create_test_files():
    """创建测试用的目录结构和 .ts 文件"""
    # 创建临时目录
    test_dir = Path(tempfile.mkdtemp(prefix="ts_test_"))
    print(f"创建测试目录: {test_dir}")
    
    # 创建子目录结构
    (test_dir / "subfolder1").mkdir()
    (test_dir / "subfolder2").mkdir()
    (test_dir / "subfolder1" / "deep").mkdir()
    
    # 创建测试文件路径
    test_files = [
        "video1.ts",
        "movie.ts", 
        "subfolder1/video2.ts",
        "subfolder1/deep/video3.ts",
        "subfolder2/video4.ts"
    ]
    
    created_files = []
    
    for file_path in test_files:
        full_path = test_dir / file_path
        print(f"创建测试文件: {full_path}")
        
        # 尝试创建真实的.ts文件
        if create_test_ts_file(full_path, duration=2):
            created_files.append(full_path)
            print(f"  ✅ 成功创建: {full_path}")
        else:
            # 如果无法创建真实文件，创建空文件用于基本测试
            full_path.touch()
            created_files.append(full_path)
            print(f"  ⚠️  创建空文件: {full_path}")
    
    # 创建一些非.ts文件，应该被忽略
    (test_dir / "not_video.txt").touch()
    (test_dir / "another.avi").touch()
    
    return test_dir, created_files


def test_ffmpeg_check():
    """测试ffmpeg检查功能"""
    print("\n" + "="*60)
    print("测试 1: FFmpeg 可用性检查")
    print("="*60)
    
    ffmpeg_available = check_ffmpeg()
    print(f"FFmpeg 可用: {ffmpeg_available}")
    
    if not ffmpeg_available:
        print("⚠️  警告: FFmpeg 不可用，某些测试可能会失败")
        print("请安装 FFmpeg:")
        print("  macOS: brew install ffmpeg")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  Windows: 从 https://ffmpeg.org/download.html 下载")
    else:
        print("✅ FFmpeg 检查通过")
    
    return ffmpeg_available


def test_dry_run():
    """测试预览模式"""
    print("\n" + "="*60)
    print("测试 2: 预览模式")
    print("="*60)
    
    test_dir, created_files = create_test_files()
    
    try:
        success, error, errors = convert_ts_to_mp4(str(test_dir), dry_run=True)
        print(f"\n预览模式结果: 成功={success}, 错误={error}")
        
        # 验证文件没有被实际转换
        ts_files = list(test_dir.rglob("*.ts"))
        mp4_files = list(test_dir.rglob("*.mp4"))
        
        print(f"预览后仍有 {len(ts_files)} 个 .ts 文件")
        print(f"预览后有 {len(mp4_files)} 个 .mp4 文件")
        
        expected_ts_count = len(created_files)
        assert len(ts_files) == expected_ts_count, f"应该有{expected_ts_count}个ts文件，实际有{len(ts_files)}个"
        assert len(mp4_files) == 0, f"预览模式不应该创建mp4文件，实际有{len(mp4_files)}个"
        
        print("✅ 预览模式测试通过")
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)


def test_actual_conversion():
    """测试实际转换"""
    print("\n" + "="*60)
    print("测试 3: 实际转换")
    print("="*60)
    
    if not check_ffmpeg():
        print("⚠️  跳过实际转换测试: FFmpeg 不可用")
        return
    
    test_dir, created_files = create_test_files()
    
    try:
        success, error, errors = convert_ts_to_mp4(str(test_dir), dry_run=False, quality='copy')
        print(f"\n实际转换结果: 成功={success}, 错误={error}")
        
        # 验证文件被正确转换
        ts_files = list(test_dir.rglob("*.ts"))
        mp4_files = list(test_dir.rglob("*.mp4"))
        
        print(f"转换后有 {len(ts_files)} 个 .ts 文件")
        print(f"转换后有 {len(mp4_files)} 个 .mp4 文件")
        
        # 检查是否有真实的视频文件被创建
        real_video_files = [f for f in created_files if f.stat().st_size > 100]  # 大于100字节的文件
        
        if real_video_files:
            # 如果有真实的视频文件，应该成功转换
            assert success > 0, "应该有成功转换的文件"
            print(f"✅ 成功转换了 {success} 个文件")
        else:
            # 如果只有空文件，可能会转换失败，这是正常的
            print(f"ℹ️  转换了 {success} 个文件，{error} 个失败（可能因为是空文件）")
        
        print("✅ 实际转换测试完成")
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)


def test_conflict_handling():
    """测试文件冲突处理"""
    print("\n" + "="*60)
    print("测试 4: 文件冲突处理")
    print("="*60)
    
    test_dir, created_files = create_test_files()
    
    try:
        # 先创建一个同名的 .mp4 文件
        conflict_mp4 = test_dir / "video1.mp4"
        conflict_mp4.touch()
        print(f"创建冲突文件: {conflict_mp4}")
        
        success, error, errors = convert_ts_to_mp4(str(test_dir), dry_run=False, quality='copy')
        print(f"\n冲突处理结果: 成功={success}, 错误={error}")
        
        # 验证冲突文件被跳过
        ts_files = list(test_dir.rglob("*.ts"))
        mp4_files = list(test_dir.rglob("*.mp4"))
        
        print(f"处理后有 {len(ts_files)} 个 .ts 文件")
        print(f"处理后有 {len(mp4_files)} 个 .mp4 文件")
        
        # video1.ts 应该因为冲突而没有被转换
        video1_ts = test_dir / "video1.ts"
        assert video1_ts.exists(), "video1.ts应该因为冲突而保留"
        
        # 冲突的mp4文件应该仍然存在
        assert conflict_mp4.exists(), "冲突的mp4文件应该保留"
        
        print("✅ 文件冲突处理测试通过")
        
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)


def test_nonexistent_directory():
    """测试不存在的目录"""
    print("\n" + "="*60)
    print("测试 5: 不存在的目录")
    print("="*60)
    
    nonexistent_dir = "/this/directory/does/not/exist"
    success, error, errors = convert_ts_to_mp4(nonexistent_dir, dry_run=True)
    
    print(f"不存在目录测试结果: 成功={success}, 错误={error}")
    assert error == 1, "应该返回错误"
    assert success == 0, "不应该有成功的操作"
    
    print("✅ 不存在目录测试通过")


def main():
    """运行所有测试"""
    print("开始运行 TS to MP4 转换功能测试")
    
    try:
        ffmpeg_available = test_ffmpeg_check()
        test_dry_run()
        
        if ffmpeg_available:
            test_actual_conversion()
            test_conflict_handling()
        else:
            print("\n⚠️  跳过需要FFmpeg的测试")
        
        test_nonexistent_directory()
        
        print("\n" + "="*60)
        print("🎉 所有测试都完成了！")
        if not ffmpeg_available:
            print("⚠️  注意: 某些测试因FFmpeg不可用而被跳过")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        raise


if __name__ == "__main__":
    main()
