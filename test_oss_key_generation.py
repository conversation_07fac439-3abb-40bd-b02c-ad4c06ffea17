#!/usr/bin/env python3
"""
测试OSS key生成逻辑
"""

from pathlib import Path
import sys

def generate_oss_key(file_path: Path, base_dir: Path) -> str:
    """
    生成OSS key
    规则：video + 文件所在目录往上4级目录结构

    Args:
        file_path: 文件完整路径
        base_dir: 基础目录路径

    Returns:
        str: OSS key
    """
    print(f"🔍 调试信息:")
    print(f"  文件路径: {file_path}")
    print(f"  基础目录: {base_dir}")

    # 获取相对于基础目录的路径
    try:
        relative_path = file_path.relative_to(base_dir)
        print(f"  相对路径: {relative_path}")
        # 获取目录部分（不包括文件名）
        relative_dir_parts = relative_path.parent.parts
        print(f"  相对目录部分: {relative_dir_parts}")

        # 如果文件直接在基础目录下，使用基础目录的最后几级作为目录结构
        if len(relative_dir_parts) == 0:
            print(f"  文件直接在基础目录下")
            # 使用基础目录的最后4级作为目录结构
            base_dir_parts = base_dir.parts
            print(f"  基础目录部分: {base_dir_parts}")
            dir_parts = base_dir_parts[-4:] if len(base_dir_parts) > 4 else base_dir_parts
            print(f"  使用基础目录层级: {dir_parts}")
        else:
            print(f"  文件在子目录中")
            # 组合基础目录的部分层级 + 相对目录
            base_dir_parts = base_dir.parts
            print(f"  基础目录部分: {base_dir_parts}")
            # 取基础目录的最后2级 + 相对目录的所有层级，总共不超过4级
            base_levels = base_dir_parts[-2:] if len(base_dir_parts) >= 2 else base_dir_parts
            print(f"  基础目录取2级: {base_levels}")
            combined_parts = base_levels + relative_dir_parts
            print(f"  组合后: {combined_parts}")
            dir_parts = combined_parts[-4:] if len(combined_parts) > 4 else combined_parts
            print(f"  最终取4级: {dir_parts}")

    except ValueError:
        print(f"  文件不在基础目录下，使用绝对路径")
        # 如果文件不在基础目录下，使用绝对路径的最后4级目录
        dir_parts = file_path.parent.parts
        print(f"  绝对目录部分: {dir_parts}")
        dir_parts = dir_parts[-4:] if len(dir_parts) > 4 else dir_parts
        print(f"  截取后目录部分: {dir_parts}")

    # 清理目录部分，移除空字符串和根目录标识
    original_dir_parts = dir_parts
    dir_parts = [part for part in dir_parts if part and part != '/']
    print(f"  清理前: {original_dir_parts}")
    print(f"  清理后: {dir_parts}")

    # 确保我们有足够的目录层级
    if len(dir_parts) == 0:
        # 如果没有目录层级，使用文件名作为目录
        dir_parts = [file_path.stem]
        print(f"  使用文件名作为目录: {dir_parts}")

    # 构建OSS key：video + 目录结构 + 文件名
    oss_key_parts = ["video"] + list(dir_parts) + [file_path.name]
    oss_key = "/".join(oss_key_parts)
    print(f"  OSS key部分: {oss_key_parts}")
    print(f"  最终OSS key: {oss_key}")

    return oss_key


def test_oss_key_generation():
    """测试各种路径情况下的OSS key生成"""
    
    test_cases = [
        {
            "name": "实际场景：文件直接在基础目录下",
            "file_path": "/Volumes/WD_BLACK/副本/人教/七年级/上册/1.1正数和负数/1.1正数和负数（第1课时）_视频课程.mp4",
            "base_dir": "/Volumes/WD_BLACK/副本/人教/七年级/上册/1.1正数和负数",
            "expected_levels": 4
        },
        {
            "name": "深层目录结构",
            "file_path": "/Volumes/WD_BLACK/人教_副本/八年级/上册/14.3.2.1_因式分解/第一课时/lesson1.mp4",
            "base_dir": "/Volumes/WD_BLACK/人教_副本/八年级",
            "expected_levels": 4
        },
        {
            "name": "浅层目录结构",
            "file_path": "/data/videos/math/lesson1.mp4",
            "base_dir": "/data/videos",
            "expected_levels": 1
        },
        {
            "name": "根目录文件",
            "file_path": "/videos/lesson1.mp4",
            "base_dir": "/videos",
            "expected_levels": 0
        },
        {
            "name": "超过4级目录",
            "file_path": "/data/2024/subjects/math/grade8/chapter1/section1/lesson1/video.mp4",
            "base_dir": "/data",
            "expected_levels": 4
        },
        {
            "name": "文件不在基础目录下",
            "file_path": "/other/path/to/video.mp4",
            "base_dir": "/base/dir",
            "expected_levels": 3
        }
    ]
    
    print("=" * 80)
    print("🧪 OSS Key生成测试")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print("-" * 60)
        
        file_path = Path(test_case["file_path"])
        base_dir = Path(test_case["base_dir"])
        
        oss_key = generate_oss_key(file_path, base_dir)
        
        # 分析结果
        oss_parts = oss_key.split("/")
        video_prefix = oss_parts[0]
        dir_levels = oss_parts[1:-1]  # 除了video前缀和文件名
        filename = oss_parts[-1]
        
        print(f"\n📊 结果分析:")
        print(f"  前缀: {video_prefix}")
        print(f"  目录层级数: {len(dir_levels)}")
        print(f"  目录层级: {dir_levels}")
        print(f"  文件名: {filename}")
        print(f"  完整OSS key: {oss_key}")
        
        # 验证
        if video_prefix == "video":
            print("  ✅ 前缀正确")
        else:
            print("  ❌ 前缀错误")
            
        if len(dir_levels) <= 4:
            print(f"  ✅ 目录层级数合理 ({len(dir_levels)} <= 4)")
        else:
            print(f"  ❌ 目录层级数过多 ({len(dir_levels)} > 4)")
        
        print("\n" + "=" * 80)


if __name__ == "__main__":
    test_oss_key_generation()
