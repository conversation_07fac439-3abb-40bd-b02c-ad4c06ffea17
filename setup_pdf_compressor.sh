#!/bin/bash

# PDF压缩工具环境配置脚本

echo "🗜️ PDF压缩工具 - 环境配置"
echo "================================================"

# 检查Python环境
echo "🐍 检查Python环境..."
if command -v python3 &> /dev/null; then
    echo "✅ Python3 已安装: $(python3 --version)"
else
    echo "❌ 请先安装Python3"
    exit 1
fi

# 检查pip
if command -v pip3 &> /dev/null; then
    echo "✅ pip3 已安装"
else
    echo "❌ 请先安装pip3"
    exit 1
fi

# 安装Python依赖
echo ""
echo "📦 安装Python依赖包..."

dependencies=("PyPDF2" "Pillow" "reportlab")

for pkg in "${dependencies[@]}"; do
    echo "   安装 $pkg..."
    pip3 install "$pkg"
    
    if [ $? -eq 0 ]; then
        echo "   ✅ $pkg 安装成功"
    else
        echo "   ❌ $pkg 安装失败"
        exit 1
    fi
done

# 检查Ghostscript
echo ""
echo "👻 检查Ghostscript..."

if command -v gs &> /dev/null; then
    echo "✅ Ghostscript 已安装: $(gs --version | head -n1)"
elif command -v gswin64c &> /dev/null; then
    echo "✅ Ghostscript 已安装 (Windows)"
else
    echo "⚠️ Ghostscript 未安装"
    echo ""
    echo "💡 Ghostscript 安装指南:"
    
    # 检测操作系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "   Ubuntu/Debian: sudo apt-get install ghostscript"
        echo "   CentOS/RHEL: sudo yum install ghostscript"
        echo "   Fedora: sudo dnf install ghostscript"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "   macOS (Homebrew): brew install ghostscript"
        echo "   macOS (MacPorts): sudo port install ghostscript"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        echo "   Windows: 下载并安装 https://www.ghostscript.com/download/gsdnld.html"
    else
        echo "   请访问 https://www.ghostscript.com/download/ 下载适合您系统的版本"
    fi
    
    echo ""
    echo "   Ghostscript 是可选的，但强烈推荐安装以获得更好的压缩效果"
fi

# 创建示例配置文件
echo ""
echo "📝 创建示例配置..."

cat > pdf_compressor_config.py << 'EOF'
#!/usr/bin/env python3
"""
PDF压缩工具配置文件

可以在这里调整默认参数
"""

# 默认压缩级别
DEFAULT_COMPRESSION_LEVEL = "medium"

# 目标压缩比例
TARGET_COMPRESSION_RATIO = 0.2  # 20%

# 图像压缩参数
IMAGE_COMPRESSION_PARAMS = {
    "low": {"quality": 80, "max_width": 1600},
    "medium": {"quality": 60, "max_width": 1200},
    "high": {"quality": 40, "max_width": 800},
    "extreme": {"quality": 30, "max_width": 600}
}

# Ghostscript质量映射
GHOSTSCRIPT_QUALITY_MAP = {
    "low": "printer",
    "medium": "ebook", 
    "high": "ebook",
    "extreme": "screen"
}

# 支持的文件扩展名
SUPPORTED_EXTENSIONS = [".pdf", ".PDF"]

# 输出目录名称（批量处理时）
OUTPUT_DIR_NAME = "compressed"

# 是否保留原始文件
KEEP_ORIGINAL = True

# 是否显示详细进度
VERBOSE = True
EOF

echo "✅ 配置文件已创建: pdf_compressor_config.py"

# 运行测试
echo ""
echo "🧪 运行功能测试..."
python3 test_pdf_compressor.py

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 环境配置完成!"
    echo ""
    echo "📖 使用方法:"
    echo "   # 压缩单个PDF文件"
    echo "   python3 pdf_compressor.py input.pdf output.pdf"
    echo ""
    echo "   # 指定压缩级别"
    echo "   python3 pdf_compressor.py input.pdf output.pdf --level high"
    echo ""
    echo "   # 批量压缩目录中的所有PDF"
    echo "   python3 pdf_compressor.py --batch /path/to/pdf/directory"
    echo ""
    echo "   # 查看帮助"
    echo "   python3 pdf_compressor.py --help"
    echo ""
    echo "🎯 压缩级别说明:"
    echo "   • low: 轻度压缩，保持高质量"
    echo "   • medium: 中等压缩，平衡质量和大小（推荐）"
    echo "   • high: 高度压缩，适合网络传输"
    echo "   • extreme: 极限压缩，最小文件大小"
    echo ""
    echo "💡 提示:"
    echo "   • 建议先用单个文件测试效果"
    echo "   • 大文件压缩可能需要较长时间"
    echo "   • 安装Ghostscript可获得更好的压缩效果"
    echo ""
    echo "================================================"
else
    echo ""
    echo "❌ 测试失败，请检查错误信息并重新配置"
fi
