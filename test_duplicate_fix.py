#!/usr/bin/env python3
"""
测试修复重复记录问题
"""

import os
import tempfile
import shutil
from pathlib import Path

def create_test_files():
    """创建测试文件"""
    temp_dir = Path(tempfile.mkdtemp(prefix="test_duplicate_"))
    
    # 创建目录结构
    test_dir = temp_dir / "人教" / "七年级" / "上册" / "1.1正数和负数"
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建两个测试MP4文件（大于1MB）
    for i in range(1, 3):
        mp4_file = test_dir / f"test_{i}.mp4"
        content = f"测试视频文件 {i}\n" * 50000  # 大于1MB
        mp4_file.write_bytes(content.encode('utf-8'))
        print(f"📹 创建测试文件: {mp4_file.name} ({len(content)/1024/1024:.1f} MB)")
    
    return temp_dir

def test_upload_no_duplicates():
    """测试上传不会产生重复记录"""
    print("🧪 测试修复重复记录问题")
    print("=" * 60)
    
    # 创建测试文件
    temp_dir = create_test_files()
    csv_file = "test_no_duplicates.csv"
    
    try:
        # 运行上传（预览模式）- 先测试预览模式
        cmd = f"python upload_mp4_to_oss.py '{temp_dir}' --dry-run --csv-file {csv_file}"
        print(f"执行命令: {cmd}")
        print()
        
        os.system(cmd)
        
        # 检查CSV文件内容
        print("\n" + "=" * 60)
        print("📄 检查CSV文件内容")
        print("=" * 60)
        
        csv_path = Path(csv_file)
        if csv_path.exists():
            with open(csv_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            print(f"📊 CSV文件总行数: {len(lines)}")
            print(f"📋 表头: {lines[0].strip()}")
            print(f"📝 数据行数: {len(lines) - 1}")
            
            # 检查是否有重复记录
            data_lines = lines[1:]  # 跳过表头
            unique_lines = set(data_lines)
            
            if len(data_lines) == len(unique_lines):
                print("✅ 没有发现重复记录")
            else:
                print(f"❌ 发现重复记录！")
                print(f"   总记录数: {len(data_lines)}")
                print(f"   唯一记录数: {len(unique_lines)}")
                print(f"   重复记录数: {len(data_lines) - len(unique_lines)}")
            
            # 显示前几行内容
            print("\n📄 CSV文件内容预览:")
            for i, line in enumerate(lines[:5]):
                print(f"   {i+1}: {line.strip()}")
            
            if len(lines) > 5:
                print(f"   ... 还有 {len(lines) - 5} 行")
        else:
            print("❌ CSV文件不存在")
    
    finally:
        # 清理测试文件
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print(f"\n🗑️  删除测试目录: {temp_dir}")
        
        csv_path = Path(csv_file)
        if csv_path.exists():
            csv_path.unlink()
            print(f"🗑️  删除CSV文件: {csv_file}")

if __name__ == "__main__":
    test_upload_no_duplicates()
