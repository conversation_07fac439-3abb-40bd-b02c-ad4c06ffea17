# PDF文件上传到阿里云OSS工具使用说明

## 功能特性

✅ **自动扫描PDF文件** - 递归扫描指定目录下所有PDF格式电子书  
✅ **智能OSS Key生成** - 根据目录结构自动生成OSS存储路径  
✅ **UUID生成** - 为每个文件生成唯一标识符  
✅ **CSV记录** - 详细记录上传信息到CSV文件  
✅ **私有访问控制** - 上传的文件设置为私有访问  
✅ **文件完整性验证** - 计算MD5哈希值确保文件完整性  
✅ **错误处理** - 完善的错误处理和重试机制  

## 环境配置

### 1. 安装依赖

```bash
pip install oss2
```

### 2. 设置环境变量

```bash
# 阿里云OSS配置
export OSS_ACCESS_KEY_ID="your_access_key_id"
export OSS_ACCESS_KEY_SECRET="your_access_key_secret"
export OSS_ENDPOINT="oss-cn-hangzhou.aliyuncs.com"  # 根据你的区域调整
export OSS_BUCKET_NAME="your_bucket_name"
```

## 使用方法

### 基本用法

```bash
# 上传指定目录下的所有PDF文件
python upload_pdf_to_oss.py /path/to/pdf/directory

# 指定CSV输出文件
python upload_pdf_to_oss.py /path/to/pdf/directory --csv my_pdf_files.csv

# 预览模式（不实际上传）
python upload_pdf_to_oss.py /path/to/pdf/directory --dry-run
```

### 目录结构示例

假设你的PDF文件目录结构如下：

```
/books/
├── 数学/
│   ├── 七年级/
│   │   ├── 上册/
│   │   │   ├── 第一章.pdf
│   │   │   └── 第二章.pdf
│   │   └── 下册/
│   │       └── 第三章.pdf
│   └── 八年级/
│       └── 上册/
│           └── 第四章.pdf
└── 语文/
    └── 七年级/
        └── 上册/
            └── 课文选读.pdf
```

### OSS Key生成规则

根据"视频所在目录往上拼接2级，最前边拼接'pdf'"的规则：

| 文件路径 | 生成的OSS Key |
|---------|---------------|
| `/books/数学/七年级/上册/第一章.pdf` | `pdf/七年级/上册/第一章.pdf` |
| `/books/数学/八年级/上册/第四章.pdf` | `pdf/八年级/上册/第四章.pdf` |
| `/books/语文/七年级/上册/课文选读.pdf` | `pdf/七年级/上册/课文选读.pdf` |

## CSV输出格式

生成的CSV文件包含以下字段：

| 字段名 | 说明 |
|--------|------|
| `uuid` | 文件唯一标识符 |
| `filename` | PDF文件名 |
| `oss_key` | OSS存储路径 |
| `file_size_mb` | 文件大小(MB) |
| `file_hash` | 文件MD5哈希值 |
| `file_path` | 本地文件完整路径 |
| `upload_time` | 上传时间 |
| `status` | 上传状态(completed/failed/error) |

### CSV示例

```csv
uuid,filename,oss_key,file_size_mb,file_hash,file_path,upload_time,status
a1b2c3d4-e5f6-7890-abcd-ef1234567890,第一章.pdf,pdf/七年级/上册/第一章.pdf,2.5,d41d8cd98f00b204e9800998ecf8427e,/books/数学/七年级/上册/第一章.pdf,2024-01-15 10:30:25,completed
```

## 错误处理

### 常见错误及解决方案

1. **OSS连接失败**
   - 检查环境变量是否正确设置
   - 确认网络连接正常
   - 验证OSS访问密钥权限

2. **文件上传失败**
   - 检查文件是否被占用
   - 确认OSS存储空间是否充足
   - 验证文件路径是否正确

3. **权限错误**
   - 确认OSS访问密钥有上传权限
   - 检查Bucket策略设置

## 高级功能

### 断点续传支持

工具会自动检查已上传的文件，避免重复上传：

```python
# 检查文件是否已存在
if self.bucket.object_exists(oss_key):
    print(f"⚠️ 文件已存在，跳过: {oss_key}")
    return True
```

### 批量处理优化

- 支持大量文件的批量处理
- 内存优化，适合处理大文件
- 进度显示和统计信息

## 安全注意事项

1. **访问控制** - 所有上传的PDF文件默认设置为私有访问
2. **密钥安全** - 不要在代码中硬编码访问密钥
3. **文件验证** - 使用MD5哈希验证文件完整性
4. **错误日志** - 详细记录上传过程中的错误信息

## 性能优化建议

1. **网络环境** - 在网络稳定的环境下运行
2. **文件大小** - 大文件建议分批上传
3. **并发控制** - 避免同时运行多个上传任务
4. **存储空间** - 确保OSS存储空间充足

## 故障排除

### 调试模式

添加详细日志输出：

```bash
# 设置详细日志
export OSS_DEBUG=1
python upload_pdf_to_oss.py /path/to/pdf/directory
```

### 检查上传状态

```python
# 验证文件是否成功上传
from upload_pdf_to_oss import PDFOSSUploader

uploader = PDFOSSUploader()
exists = uploader.bucket.object_exists("pdf/七年级/上册/第一章.pdf")
print(f"文件存在: {exists}")
```

## 扩展功能

可以根据需要扩展以下功能：

- 支持其他文件格式（DOC、DOCX等）
- 添加文件压缩功能
- 实现增量同步
- 添加文件预览功能
- 集成文件搜索功能
