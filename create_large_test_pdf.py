#!/usr/bin/env python3
"""
创建一个大的测试PDF文件用于压缩测试
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib.utils import ImageReader
from PIL import Image
import io
import os

def create_large_test_pdf(filename: str = "test_very_large.pdf") -> str:
    """创建一个大的测试PDF文件"""
    try:
        print(f"📄 创建大型测试PDF: {filename}")
        
        # 创建PDF
        c = canvas.Canvas(filename, pagesize=letter)
        width, height = letter
        
        # 添加更多页内容以增加文件大小
        for page in range(100):  # 100页
            print(f"   创建第 {page+1}/100 页...")
            
            # 添加标题
            c.setFont("Helvetica-Bold", 16)
            c.drawString(50, height - 50, f"测试文档 - 第{page+1}页")
            
            # 添加大量文本
            c.setFont("Helvetica", 10)
            for i in range(80):  # 每页80行文本
                y_pos = height - 100 - (i * 8)
                if y_pos > 50:
                    text = f"第{page+1}页第{i+1}行 - 这是用于测试PDF压缩效果的长文本内容，包含中文和英文字符。" * 3
                    c.drawString(50, y_pos, text[:100])  # 限制长度避免超出页面
            
            # 添加多个高质量图片
            for img_idx in range(4):  # 每页4个图片
                # 创建彩色图片
                img = Image.new('RGB', (300, 200), 
                              color=(255-img_idx*50, img_idx*60, 100+img_idx*40))
                
                # 添加一些图案
                pixels = img.load()
                for x in range(300):
                    for y in range(200):
                        if (x + y) % 20 < 10:
                            pixels[x, y] = (255, 255, 255)
                
                # 保存为高质量JPEG
                img_buffer = io.BytesIO()
                img.save(img_buffer, format='JPEG', quality=95)
                img_buffer.seek(0)
                
                # 计算位置
                x_pos = 50 + (img_idx % 2) * 250
                y_pos = height - 300 - (img_idx // 2) * 150
                
                if y_pos > 50:
                    c.drawImage(ImageReader(img_buffer), x_pos, y_pos, 
                              width=200, height=130)
            
            c.showPage()
        
        c.save()
        
        # 检查文件大小
        file_size = os.path.getsize(filename) / (1024 * 1024)  # MB
        print(f"✅ 创建完成: {filename}")
        print(f"📊 文件大小: {file_size:.2f} MB")
        
        return filename
        
    except Exception as e:
        print(f"❌ 创建PDF失败: {e}")
        return None

if __name__ == "__main__":
    create_large_test_pdf()
