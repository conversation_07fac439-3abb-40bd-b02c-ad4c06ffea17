#!/usr/bin/env python3
"""
匹配final_results.csv和oss_files.csv文件
生成match_result.csv文件
"""

import csv
import sys
from pathlib import Path

def match_csv_files(final_results_file: str, oss_files_file: str, output_file: str = "match_result.csv"):
    """
    匹配两个CSV文件
    
    Args:
        final_results_file: final_results.csv文件路径
        oss_files_file: oss_files.csv文件路径
        output_file: 输出文件路径
    """
    
    try:
        print(f"📄 读取文件:")
        print(f"   - final_results: {final_results_file}")
        print(f"   - oss_files: {oss_files_file}")
        
        # 检查文件是否存在
        if not Path(final_results_file).exists():
            print(f"❌ 文件不存在: {final_results_file}")
            return
        
        if not Path(oss_files_file).exists():
            print(f"❌ 文件不存在: {oss_files_file}")
            return
        
        # 读取oss_files.csv，建立oss_key到uuid的映射
        print("📋 读取oss_files.csv...")
        oss_mapping = {}
        oss_count = 0
        
        with open(oss_files_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                oss_count += 1
                uuid = row.get('uuid', '').strip()
                oss_key = row.get('oss_key', '').strip()
                
                if uuid and oss_key:
                    oss_mapping[oss_key] = uuid
        
        print(f"   ✅ 读取 {oss_count} 条oss记录")
        
        # 读取final_results.csv并进行匹配
        print("🔍 开始匹配...")
        matches = []
        total_rows = 0
        matched_count = 0
        
        with open(final_results_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                total_rows += 1
                section_id = row.get('section_id', '').strip()
                top_match_content = row.get('top_match_content', '').strip()
                
                if not section_id or not top_match_content:
                    print(f"   ⚠️  跳过第{total_rows}行（缺少必要字段）")
                    continue
                
                # 查找匹配的oss_key
                matched_uuid = None
                matched_oss_key = None
                
                for oss_key, uuid in oss_mapping.items():
                    # 检查oss_key是否完全包含top_match_content
                    if top_match_content in oss_key:
                        matched_uuid = uuid
                        matched_oss_key = oss_key
                        break
                
                if matched_uuid:
                    matches.append({
                        'section_id': section_id,
                        'uuid': matched_uuid,
                        'top_match_content': top_match_content,
                        'matched_oss_key': matched_oss_key
                    })
                    matched_count += 1
                    print(f"   ✅ 匹配成功 {matched_count}: {section_id[:8]}... -> {matched_uuid[:8]}...")
                else:
                    print(f"   ❌ 未找到匹配: {top_match_content[:50]}...")
        
        # 写入结果文件
        print(f"\n📝 写入结果到: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(['section_id', 'uuid'])
            
            # 写入匹配结果
            for match in matches:
                writer.writerow([match['section_id'], match['uuid']])
        
        # 生成详细报告
        report_file = output_file.replace('.csv', '_detailed_report.csv')
        with open(report_file, 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(['section_id', 'uuid', 'top_match_content', 'matched_oss_key'])
            
            # 写入详细匹配结果
            for match in matches:
                writer.writerow([
                    match['section_id'], 
                    match['uuid'], 
                    match['top_match_content'], 
                    match['matched_oss_key']
                ])
        
        # 输出统计信息
        print("\n" + "="*60)
        print("📊 匹配统计:")
        print("="*60)
        print(f"总记录数: {total_rows}")
        print(f"匹配成功: {matched_count}")
        print(f"匹配失败: {total_rows - matched_count}")
        print(f"匹配率: {matched_count/total_rows*100:.1f}%")
        print(f"输出文件: {output_file}")
        print(f"详细报告: {report_file}")
        print("="*60)
        
        return matches
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("📖 用法:")
        print("  python match_final_oss_files.py <final_results.csv> <oss_files.csv> [output.csv]")
        print()
        print("📝 示例:")
        print("  python match_final_oss_files.py final_results.csv oss_files.csv")
        print("  python match_final_oss_files.py final_results.csv oss_files.csv match_result.csv")
        print()
        print("📋 匹配规则:")
        print("  - 以final_results.csv的行数为准")
        print("  - 第7列top_match_content与oss_files.csv的第3列oss_key匹配")
        print("  - 如果oss_key完全包含top_match_content，则匹配成功")
        print("  - 输出section_id和uuid到新文件")
        return
    
    final_results_file = sys.argv[1]
    oss_files_file = sys.argv[2]
    output_file = sys.argv[3] if len(sys.argv) > 3 else "match_result.csv"
    
    match_csv_files(final_results_file, oss_files_file, output_file)

if __name__ == "__main__":
    main()
