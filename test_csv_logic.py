#!/usr/bin/env python3
"""
直接测试CSV写入逻辑，确认没有重复记录
"""

import csv
import tempfile
from pathlib import Path
from datetime import datetime
import uuid

def test_csv_writing_logic():
    """测试CSV写入逻辑"""
    print("🧪 测试CSV写入逻辑")
    print("=" * 50)
    
    # 创建临时CSV文件
    temp_dir = Path(tempfile.mkdtemp())
    csv_file = temp_dir / "test_csv.csv"
    
    print(f"📄 创建测试CSV: {csv_file}")
    
    # 模拟上传记录
    test_records = [
        {
            'uuid': str(uuid.uuid4()),
            'filename': 'test1.mp4',
            'oss_key': 'video/test/test1.mp4',
            'file_size_mb': '1.23',
            'file_hash': 'hash1234567890',
            'file_path': '/test/path/test1.mp4',
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'completed'
        },
        {
            'uuid': str(uuid.uuid4()),
            'filename': 'test2.mp4',
            'oss_key': 'video/test/test2.mp4',
            'file_size_mb': '2.45',
            'file_hash': 'hash0987654321',
            'file_path': '/test/path/test2.mp4',
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'completed'
        }
    ]
    
    # 模拟原来的写入逻辑（会产生重复）
    print("\n📝 模拟原来的逻辑（会重复写入）:")
    csv_file_old = temp_dir / "test_csv_old.csv"
    
    # 第一次写入（模拟主循环中的writer.writerow）
    with open(csv_file_old, 'w', newline='', encoding='utf-8') as f:
        fieldnames = ['uuid', 'filename', 'oss_key', 'file_size_mb', 'file_hash', 'file_path', 'upload_time', 'status']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        
        for record in test_records:
            writer.writerow(record)
            print(f"   ✍️  写入记录: {record['filename']}")
    
    # 第二次写入（模拟save_upload_record函数）
    for record in test_records:
        with open(csv_file_old, 'a', newline='', encoding='utf-8') as f:
            fieldnames = ['uuid', 'filename', 'oss_key', 'file_size_mb', 'file_hash', 'file_path', 'upload_time', 'status']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writerow(record)
            print(f"   ✍️  再次写入记录: {record['filename']}")
    
    # 检查旧逻辑的结果
    with open(csv_file_old, 'r', encoding='utf-8') as f:
        old_lines = f.readlines()
    
    print(f"\n📊 旧逻辑结果:")
    print(f"   总行数: {len(old_lines)}")
    print(f"   数据行数: {len(old_lines) - 1}")
    print(f"   预期数据行数: {len(test_records)}")
    print(f"   是否有重复: {'是' if len(old_lines) - 1 > len(test_records) else '否'}")
    
    # 模拟修复后的写入逻辑（不会重复）
    print("\n📝 模拟修复后的逻辑（只写入一次）:")
    csv_file_new = temp_dir / "test_csv_new.csv"
    
    with open(csv_file_new, 'w', newline='', encoding='utf-8') as f:
        fieldnames = ['uuid', 'filename', 'oss_key', 'file_size_mb', 'file_hash', 'file_path', 'upload_time', 'status']
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        
        for record in test_records:
            writer.writerow(record)
            print(f"   ✍️  写入记录: {record['filename']}")
    
    # 检查新逻辑的结果
    with open(csv_file_new, 'r', encoding='utf-8') as f:
        new_lines = f.readlines()
    
    print(f"\n📊 新逻辑结果:")
    print(f"   总行数: {len(new_lines)}")
    print(f"   数据行数: {len(new_lines) - 1}")
    print(f"   预期数据行数: {len(test_records)}")
    print(f"   是否有重复: {'是' if len(new_lines) - 1 > len(test_records) else '否'}")
    
    # 显示文件内容对比
    print(f"\n📄 旧逻辑CSV内容:")
    with open(csv_file_old, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    
    print(f"\n📄 新逻辑CSV内容:")
    with open(csv_file_new, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    
    # 清理
    import shutil
    shutil.rmtree(temp_dir)
    print(f"\n🗑️  清理测试文件: {temp_dir}")

if __name__ == "__main__":
    test_csv_writing_logic()
