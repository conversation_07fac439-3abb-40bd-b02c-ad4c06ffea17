#!/usr/bin/env python3
"""
从CSV文件生成SQL UPDATE语句
根据CSV中的字段生成批量更新语句
"""

import csv
import sys
from pathlib import Path

def generate_update_sql(csv_file_path: str, output_file: str = None):
    """
    从CSV文件生成SQL UPDATE语句
    
    Args:
        csv_file_path: CSV文件路径
        output_file: 输出SQL文件路径（可选）
    """
    
    try:
        csv_path = Path(csv_file_path)
        if not csv_path.exists():
            print(f"❌ 文件不存在: {csv_file_path}")
            return
        
        print(f"📄 读取CSV文件: {csv_file_path}")
        
        # 读取CSV文件
        with open(csv_path, 'r', encoding='utf-8') as file:
            # 检测CSV格式
            sample = file.read(1024)
            file.seek(0)
            
            # 尝试不同的分隔符
            if ',' in sample:
                delimiter = ','
            elif '\t' in sample:
                delimiter = '\t'
            elif ';' in sample:
                delimiter = ';'
            else:
                delimiter = ','
            
            reader = csv.DictReader(file, delimiter=delimiter)
            
            # 显示列名
            fieldnames = reader.fieldnames
            print(f"📋 发现列: {fieldnames}")
            
            # 检查必需的字段
            required_fields = ['id', 'ebook_file_id']
            missing_fields = []
            
            for field in required_fields:
                if field not in fieldnames:
                    # 尝试找到相似的字段名
                    similar_fields = [f for f in fieldnames if field.lower() in f.lower() or f.lower() in field.lower()]
                    if similar_fields:
                        print(f"⚠️  未找到字段 '{field}'，但发现相似字段: {similar_fields}")
                    else:
                        missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ 缺少必需字段: {missing_fields}")
                print(f"💡 请确保CSV包含以下字段: {required_fields}")
                return
            
            # 生成SQL语句
            sql_statements = []
            row_count = 0
            
            for row in reader:
                row_count += 1
                
                # 获取字段值
                id_value = row.get('id', '').strip()
                ebook_file_id_value = row.get('ebook_file_id', '').strip()
                
                # 跳过空值行
                if not id_value or not ebook_file_id_value:
                    print(f"⚠️  跳过第{row_count}行（包含空值）: id='{id_value}', ebook_file_id='{ebook_file_id_value}'")
                    continue
                
                # 生成SQL语句
                sql = f"UPDATE math_textbooks SET ebook_file_id = '{ebook_file_id_value}' WHERE id = '{id_value}';"
                sql_statements.append(sql)
            
            print(f"✅ 成功处理 {len(sql_statements)} 条记录")
            
            # 输出SQL语句
            if output_file:
                output_path = Path(output_file)
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write("-- 批量更新math_textbooks表的ebook_file_id字段\n")
                    f.write(f"-- 生成时间: {csv_path.name}\n")
                    f.write(f"-- 总计: {len(sql_statements)} 条更新语句\n\n")
                    
                    for sql in sql_statements:
                        f.write(sql + '\n')
                
                print(f"📝 SQL语句已保存到: {output_file}")
            else:
                # 直接输出到控制台
                print("\n" + "="*60)
                print("生成的SQL语句:")
                print("="*60)
                
                for i, sql in enumerate(sql_statements, 1):
                    print(f"-- 第{i}条")
                    print(sql)
                    print()
                
                print("="*60)
                print(f"总计: {len(sql_statements)} 条UPDATE语句")
            
            return sql_statements
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("📖 用法:")
        print("  python generate_update_sql.py <csv_file> [output_sql_file]")
        print()
        print("📝 示例:")
        print("  python generate_update_sql.py data.csv")
        print("  python generate_update_sql.py data.csv update_statements.sql")
        print()
        print("📋 CSV文件要求:")
        print("  - 必须包含 'id' 和 'ebook_file_id' 列")
        print("  - 支持逗号、制表符、分号分隔")
        return
    
    csv_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    generate_update_sql(csv_file, output_file)

if __name__ == "__main__":
    main()
