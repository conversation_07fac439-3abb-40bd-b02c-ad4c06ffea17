# 快速开始 - TS转MP4转换

## 🚀 最简单的使用方法

### 1. 预览要转换的文件
```bash
python simple_ts_to_mp4.py /path/to/your/videos --dry-run
```

### 2. 开始转换
```bash
python simple_ts_to_mp4.py /path/to/your/videos
```

就这么简单！

## 📋 完整示例

假设你有一个包含TS文件的目录：`/Volumes/WD_BLACK/人教_副本/八年级`

### 步骤1：预览
```bash
python simple_ts_to_mp4.py "/Volumes/WD_BLACK/人教_副本/八年级" --dry-run
```

输出示例：
```
============================================================
🎬 简单TS转MP4转换器
============================================================
📂 处理目录: /Volumes/WD_BLACK/人教_副本/八年级
🔍 运行模式: 预览模式 (不会实际转换文件)
============================================================
📁 找到 3 个有效的 .ts 文件
📊 总文件大小: 968.1 MB
⚙️  转换策略: 直接复制流 (-c:v copy -c:a copy)
⏱️  超时设置: 300 秒/文件
------------------------------------------------------------
[1/3] 📹 因式分解—公式法（第一课时）_视频.ts (219.9 MB)
  [预览] 因式分解—公式法（第一课时）_视频.ts -> 因式分解—公式法（第一课时）_视频.mp4
  📈 总进度: 22.7% (219.9/968.1 MB)

[2/3] 📹 因式分解—公式法（第二课时）_视频.ts (259.1 MB)
  [预览] 因式分解—公式法（第二课时）_视频.ts -> 因式分解—公式法（第二课时）_视频.mp4
  📈 总进度: 49.5% (479.1/968.1 MB)

[3/3] 📹 因式分解—公式法（第三课时）_视频.ts (489.0 MB)
  [预览] 因式分解—公式法（第三课时）_视频.ts -> 因式分解—公式法（第三课时）_视频.mp4
  📈 总进度: 100.0% (968.1/968.1 MB)

============================================================
🏁 处理完成 (总耗时: 0.0秒):
  ✅ 成功: 3 个文件
  ❌ 失败: 0 个文件
```

### 步骤2：执行转换
```bash
python simple_ts_to_mp4.py "/Volumes/WD_BLACK/人教_副本/八年级"
```

输出示例：
```
============================================================
🎬 简单TS转MP4转换器
============================================================
📂 处理目录: /Volumes/WD_BLACK/人教_副本/八年级
🚀 运行模式: 实际转换
============================================================
📁 找到 3 个有效的 .ts 文件
📊 总文件大小: 968.1 MB
⚙️  转换策略: 直接复制流 (-c:v copy -c:a copy)
⏱️  超时设置: 300 秒/文件
------------------------------------------------------------
[1/3] 📹 因式分解—公式法（第一课时）_视频.ts (219.9 MB)
  🔄 转换中: 因式分解—公式法（第一课时）_视频.ts -> 因式分解—公式法（第一课时）_视频.mp4
  ✅ 转换成功: 0.6s, 输出: 210.9 MB (-4.1%)
  📈 总进度: 22.7% (219.9/968.1 MB)

[2/3] 📹 因式分解—公式法（第二课时）_视频.ts (259.1 MB)
  🔄 转换中: 因式分解—公式法（第二课时）_视频.ts -> 因式分解—公式法（第二课时）_视频.mp4
  ✅ 转换成功: 0.6s, 输出: 249.8 MB (-3.6%)
  📈 总进度: 49.5% (479.1/968.1 MB)

[3/3] 📹 因式分解—公式法（第三课时）_视频.ts (489.0 MB)
  🔄 转换中: 因式分解—公式法（第三课时）_视频.ts -> 因式分解—公式法（第三课时）_视频.mp4
  ✅ 转换成功: 1.1s, 输出: 474.0 MB (-3.1%)
  📈 总进度: 100.0% (968.1/968.1 MB)

============================================================
🏁 处理完成 (总耗时: 2.3秒):
  ✅ 成功: 3 个文件
  ❌ 失败: 0 个文件
  ⚡ 平均转换速度: 0.8秒/文件
```

## 🎯 常用选项

### 避免覆盖已存在的文件
```bash
python simple_ts_to_mp4.py /path/to/videos --add-suffix
```
这会生成类似 `video_converted.mp4` 的文件名

### 覆盖已存在的文件
```bash
python simple_ts_to_mp4.py /path/to/videos --overwrite
```

### 增加超时时间（适用于大文件）
```bash
python simple_ts_to_mp4.py /path/to/videos --timeout 600
```

### 显示详细错误信息
```bash
python simple_ts_to_mp4.py /path/to/videos --verbose
```

## ⚡ 为什么这么快？

这个工具使用的FFmpeg命令是：
```bash
ffmpeg -i input.ts -c:v copy -c:a copy output.mp4
```

- `-c:v copy`：直接复制视频流，不重新编码
- `-c:a copy`：直接复制音频流，不重新编码

这种方法：
- ✅ 速度最快（几乎瞬间完成）
- ✅ 质量无损（完全保持原始质量）
- ✅ 文件大小略微减小（去除TS容器开销）
- ❌ 要求TS文件格式兼容MP4容器

## 🔧 如果转换失败怎么办？

如果简单转换器失败，可以尝试：

1. **使用强化版转换器**（多种策略）：
```bash
python robust_ts_converter.py /path/to/videos
```

2. **诊断文件问题**：
```bash
python diagnose_ts_file.py /path/to/problematic/file.ts
```

3. **使用标准转换器**（重新编码）：
```bash
python convert_ts_to_mp4.py /path/to/videos --quality medium
```

## 📝 注意事项

1. **备份重要文件**：虽然转换过程安全，但建议备份重要文件
2. **磁盘空间**：确保有足够空间存储转换后的文件
3. **FFmpeg依赖**：需要安装FFmpeg
   - macOS: `brew install ffmpeg`
   - Ubuntu: `sudo apt install ffmpeg`
   - Windows: 从官网下载安装

## 🎉 就是这么简单！

大多数情况下，你只需要两个命令：
1. `python simple_ts_to_mp4.py /path/to/videos --dry-run` （预览）
2. `python simple_ts_to_mp4.py /path/to/videos` （转换）

享受快速的TS转MP4转换吧！
