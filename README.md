# MP4 to TS Renamer

这个工具可以递归地将指定目录下所有的 `.mp4` 文件重命名为 `.ts` 文件。

## 功能特点

- 递归搜索指定目录及其所有子目录
- 批量重命名所有 `.mp4` 文件为 `.ts` 文件
- 支持预览模式，可以先查看将要重命名的文件
- 安全检查：如果目标文件已存在，会跳过重命名
- 详细的错误处理和日志输出
- 支持命令行参数

## 使用方法

### 基本用法

```bash
# 重命名当前目录下所有 .mp4 文件
python rename_mp4_to_ts.py .

# 重命名指定目录下所有 .mp4 文件
python rename_mp4_to_ts.py /path/to/your/videos
```

### 预览模式

在实际重命名之前，可以使用预览模式查看将要重命名的文件：

```bash
# 预览模式 - 只显示将要重命名的文件，不实际执行
python rename_mp4_to_ts.py /path/to/videos --dry-run
```

### 详细输出

```bash
# 显示详细的错误信息
python rename_mp4_to_ts.py /path/to/videos --verbose
```

## 命令行参数

- `directory`: 要处理的目录路径（必需）
- `--dry-run`: 预览模式，只显示将要重命名的文件
- `--verbose`, `-v`: 显示详细信息，包括错误详情

## 示例输出

### 正常执行
```
处理目录: /Users/<USER>/Videos
运行模式: 实际重命名
--------------------------------------------------
找到 3 个 .mp4 文件
成功重命名: /Users/<USER>/Videos/video1.mp4 -> /Users/<USER>/Videos/video1.ts
成功重命名: /Users/<USER>/Videos/subfolder/video2.mp4 -> /Users/<USER>/Videos/subfolder/video2.ts
成功重命名: /Users/<USER>/Videos/video3.mp4 -> /Users/<USER>/Videos/video3.ts
--------------------------------------------------
处理完成:
  成功: 3 个文件
  失败: 0 个文件
```

### 预览模式
```
处理目录: /Users/<USER>/Videos
运行模式: 预览模式 (不会实际重命名文件)
--------------------------------------------------
找到 3 个 .mp4 文件
[预览] /Users/<USER>/Videos/video1.mp4 -> /Users/<USER>/Videos/video1.ts
[预览] /Users/<USER>/Videos/subfolder/video2.mp4 -> /Users/<USER>/Videos/subfolder/video2.ts
[预览] /Users/<USER>/Videos/video3.mp4 -> /Users/<USER>/Videos/video3.ts
--------------------------------------------------
处理完成:
  成功: 3 个文件
  失败: 0 个文件
```

## 安全特性

1. **文件存在检查**: 如果目标 `.ts` 文件已存在，会跳过重命名以避免覆盖
2. **目录验证**: 检查指定的路径是否存在且为目录
3. **错误处理**: 捕获并报告所有重命名过程中的错误
4. **预览模式**: 可以先预览要重命名的文件，确认无误后再执行

## 注意事项

- 请确保有足够的权限访问和修改目标目录中的文件
- 建议先使用 `--dry-run` 参数预览要重命名的文件
- 如果目标 `.ts` 文件已存在，程序会跳过该文件的重命名操作
- 程序会递归处理所有子目录中的 `.mp4` 文件

## 系统要求

- Python 3.6 或更高版本
- 无需额外依赖，使用 Python 标准库
