#!/usr/bin/env python3
"""
MP4 to TS Renamer

This script recursively finds all .mp4 files in a specified directory
and renames them to .ts files.
"""

import os
import sys
import argparse
from pathlib import Path


def rename_mp4_to_ts(directory_path, dry_run=False):
    """
    Recursively rename all .mp4 files to .ts files in the specified directory.
    
    Args:
        directory_path (str): The directory path to search
        dry_run (bool): If True, only show what would be renamed without actually renaming
    
    Returns:
        tuple: (success_count, error_count, errors)
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"错误: 目录 '{directory_path}' 不存在")
        return 0, 1, [f"目录 '{directory_path}' 不存在"]
    
    if not directory.is_dir():
        print(f"错误: '{directory_path}' 不是一个目录")
        return 0, 1, [f"'{directory_path}' 不是一个目录"]
    
    # 递归查找所有.mp4文件
    mp4_files = list(directory.rglob("*.mp4"))
    
    if not mp4_files:
        print(f"在目录 '{directory_path}' 中没有找到任何 .mp4 文件")
        return 0, 0, []
    
    print(f"找到 {len(mp4_files)} 个 .mp4 文件")
    
    success_count = 0
    error_count = 0
    errors = []
    
    for mp4_file in mp4_files:
        # 构造新的.ts文件名
        ts_file = mp4_file.with_suffix('.ts')
        
        if dry_run:
            print(f"[预览] {mp4_file} -> {ts_file}")
            success_count += 1
        else:
            try:
                # 检查目标文件是否已存在
                if ts_file.exists():
                    error_msg = f"目标文件已存在: {ts_file}"
                    print(f"跳过: {error_msg}")
                    errors.append(error_msg)
                    error_count += 1
                    continue
                
                # 重命名文件
                mp4_file.rename(ts_file)
                print(f"成功重命名: {mp4_file} -> {ts_file}")
                success_count += 1
                
            except Exception as e:
                error_msg = f"重命名失败 {mp4_file}: {str(e)}"
                print(f"错误: {error_msg}")
                errors.append(error_msg)
                error_count += 1
    
    return success_count, error_count, errors


def main():
    parser = argparse.ArgumentParser(
        description="递归重命名目录下所有.mp4文件为.ts文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python rename_mp4_to_ts.py /path/to/videos
  python rename_mp4_to_ts.py /path/to/videos --dry-run
  python rename_mp4_to_ts.py .
        """
    )
    
    parser.add_argument(
        "directory",
        help="要处理的目录路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，只显示将要重命名的文件，不实际执行重命名操作"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    args = parser.parse_args()
    
    # 转换为绝对路径
    directory_path = os.path.abspath(args.directory)
    
    print(f"处理目录: {directory_path}")
    if args.dry_run:
        print("运行模式: 预览模式 (不会实际重命名文件)")
    else:
        print("运行模式: 实际重命名")
    
    print("-" * 50)
    
    # 执行重命名操作
    success_count, error_count, errors = rename_mp4_to_ts(
        directory_path, 
        dry_run=args.dry_run
    )
    
    print("-" * 50)
    print(f"处理完成:")
    print(f"  成功: {success_count} 个文件")
    print(f"  失败: {error_count} 个文件")
    
    if errors and args.verbose:
        print("\n错误详情:")
        for error in errors:
            print(f"  - {error}")
    
    # 返回适当的退出码
    sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
