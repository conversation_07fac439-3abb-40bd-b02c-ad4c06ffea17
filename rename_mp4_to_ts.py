#!/usr/bin/env python3
"""
MP4 to TS Renamer

This script recursively finds all .mp4 files in a specified directory
and renames them to .ts files.
"""

import os
import sys
import argparse
import subprocess
import shutil
from pathlib import Path


def rename_mp4_to_ts(directory_path, dry_run=False):
    """
    Recursively rename all .mp4 files to .ts files in the specified directory.
    
    Args:
        directory_path (str): The directory path to search
        dry_run (bool): If True, only show what would be renamed without actually renaming
    
    Returns:
        tuple: (success_count, error_count, errors)
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"错误: 目录 '{directory_path}' 不存在")
        return 0, 1, [f"目录 '{directory_path}' 不存在"]
    
    if not directory.is_dir():
        print(f"错误: '{directory_path}' 不是一个目录")
        return 0, 1, [f"'{directory_path}' 不是一个目录"]
    
    # 递归查找所有.mp4文件
    mp4_files = list(directory.rglob("*.mp4"))
    
    if not mp4_files:
        print(f"在目录 '{directory_path}' 中没有找到任何 .mp4 文件")
        return 0, 0, []
    
    print(f"找到 {len(mp4_files)} 个 .mp4 文件")
    
    success_count = 0
    error_count = 0
    errors = []
    
    for mp4_file in mp4_files:
        # 构造新的.ts文件名
        ts_file = mp4_file.with_suffix('.ts')
        
        if dry_run:
            print(f"[预览] {mp4_file} -> {ts_file}")
            success_count += 1
        else:
            try:
                # 检查目标文件是否已存在
                if ts_file.exists():
                    error_msg = f"目标文件已存在: {ts_file}"
                    print(f"跳过: {error_msg}")
                    errors.append(error_msg)
                    error_count += 1
                    continue
                
                # 重命名文件
                mp4_file.rename(ts_file)
                print(f"成功重命名: {mp4_file} -> {ts_file}")
                success_count += 1
                
            except Exception as e:
                error_msg = f"重命名失败 {mp4_file}: {str(e)}"
                print(f"错误: {error_msg}")
                errors.append(error_msg)
                error_count += 1
    
    return success_count, error_count, errors


def check_ffmpeg():
    """
    检查系统是否安装了ffmpeg

    Returns:
        bool: True if ffmpeg is available, False otherwise
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True,
                              text=True,
                              timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False


def convert_ts_to_mp4(directory_path, dry_run=False, quality='medium', overwrite=False):
    """
    Recursively convert all .ts files to .mp4 files in the specified directory using ffmpeg.

    Args:
        directory_path (str): The directory path to search
        dry_run (bool): If True, only show what would be converted without actually converting
        quality (str): Conversion quality - 'high', 'medium', 'low'
        overwrite (bool): If True, overwrite existing .mp4 files

    Returns:
        tuple: (success_count, error_count, errors)
    """
    directory = Path(directory_path)

    if not directory.exists():
        print(f"错误: 目录 '{directory_path}' 不存在")
        return 0, 1, [f"目录 '{directory_path}' 不存在"]

    if not directory.is_dir():
        print(f"错误: '{directory_path}' 不是一个目录")
        return 0, 1, [f"'{directory_path}' 不是一个目录"]

    # 检查ffmpeg是否可用
    if not check_ffmpeg():
        error_msg = "错误: 系统中未找到ffmpeg，请先安装ffmpeg"
        print(error_msg)
        return 0, 1, [error_msg]

    # 递归查找所有.ts文件
    ts_files = list(directory.rglob("*.ts"))

    if not ts_files:
        print(f"在目录 '{directory_path}' 中没有找到任何 .ts 文件")
        return 0, 0, []

    print(f"找到 {len(ts_files)} 个 .ts 文件")

    # 设置ffmpeg质量参数
    quality_settings = {
        'high': ['-c:v', 'libx264', '-crf', '18', '-c:a', 'aac', '-b:a', '192k'],
        'medium': ['-c:v', 'libx264', '-crf', '23', '-c:a', 'aac', '-b:a', '128k'],
        'low': ['-c:v', 'libx264', '-crf', '28', '-c:a', 'aac', '-b:a', '96k']
    }

    ffmpeg_params = quality_settings.get(quality, quality_settings['medium'])

    success_count = 0
    error_count = 0
    errors = []

    for ts_file in ts_files:
        # 构造新的.mp4文件名
        mp4_file = ts_file.with_suffix('.mp4')

        if dry_run:
            print(f"[预览] {ts_file} -> {mp4_file}")
            success_count += 1
        else:
            try:
                # 检查目标文件是否已存在
                if mp4_file.exists() and not overwrite:
                    error_msg = f"目标文件已存在: {mp4_file} (使用 --overwrite 强制覆盖)"
                    print(f"跳过: {error_msg}")
                    errors.append(error_msg)
                    error_count += 1
                    continue

                print(f"正在转换: {ts_file} -> {mp4_file}")

                # 构建ffmpeg命令
                cmd = ['ffmpeg', '-i', str(ts_file)] + ffmpeg_params + ['-y' if overwrite else '-n', str(mp4_file)]

                # 执行ffmpeg转换
                result = subprocess.run(cmd,
                                      capture_output=True,
                                      text=True,
                                      timeout=300)  # 5分钟超时

                if result.returncode == 0:
                    print(f"成功转换: {ts_file} -> {mp4_file}")
                    success_count += 1
                else:
                    error_msg = f"转换失败 {ts_file}: {result.stderr}"
                    print(f"错误: {error_msg}")
                    errors.append(error_msg)
                    error_count += 1

            except subprocess.TimeoutExpired:
                error_msg = f"转换超时 {ts_file}: 转换时间超过5分钟"
                print(f"错误: {error_msg}")
                errors.append(error_msg)
                error_count += 1
            except Exception as e:
                error_msg = f"转换失败 {ts_file}: {str(e)}"
                print(f"错误: {error_msg}")
                errors.append(error_msg)
                error_count += 1

    return success_count, error_count, errors


def main():
    parser = argparse.ArgumentParser(
        description="递归重命名目录下所有.mp4文件为.ts文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python rename_mp4_to_ts.py /path/to/videos
  python rename_mp4_to_ts.py /path/to/videos --dry-run
  python rename_mp4_to_ts.py .
        """
    )
    
    parser.add_argument(
        "directory",
        help="要处理的目录路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，只显示将要重命名的文件，不实际执行重命名操作"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    args = parser.parse_args()
    
    # 转换为绝对路径
    directory_path = os.path.abspath(args.directory)
    
    print(f"处理目录: {directory_path}")
    if args.dry_run:
        print("运行模式: 预览模式 (不会实际重命名文件)")
    else:
        print("运行模式: 实际重命名")
    
    print("-" * 50)
    
    # 执行重命名操作
    success_count, error_count, errors = rename_mp4_to_ts(
        directory_path, 
        dry_run=args.dry_run
    )
    
    print("-" * 50)
    print(f"处理完成:")
    print(f"  成功: {success_count} 个文件")
    print(f"  失败: {error_count} 个文件")
    
    if errors and args.verbose:
        print("\n错误详情:")
        for error in errors:
            print(f"  - {error}")
    
    # 返回适当的退出码
    sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
