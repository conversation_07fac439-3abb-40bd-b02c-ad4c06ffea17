#!/usr/bin/env python3
"""
TS文件诊断工具

用于分析.ts文件的详细信息，帮助诊断转换问题
"""

import os
import sys
import argparse
import subprocess
import json
from pathlib import Path


def check_ffmpeg_tools():
    """检查FFmpeg工具是否可用"""
    tools = ['ffmpeg', 'ffprobe']
    available = {}
    
    for tool in tools:
        try:
            result = subprocess.run([tool, '-version'], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            available[tool] = result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            available[tool] = False
    
    return available


def get_file_info(file_path):
    """获取文件基本信息"""
    try:
        stat = file_path.stat()
        return {
            'size_bytes': stat.st_size,
            'size_mb': stat.st_size / (1024 * 1024),
            'readable': os.access(file_path, os.R_OK),
            'writable': os.access(file_path, os.W_OK),
            'exists': file_path.exists()
        }
    except Exception as e:
        return {'error': str(e)}


def probe_with_ffprobe(file_path):
    """使用ffprobe分析文件"""
    try:
        # 获取详细格式信息
        cmd = [
            'ffprobe', '-v', 'quiet', '-print_format', 'json',
            '-show_format', '-show_streams', '-show_error',
            str(file_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return {'error': result.stderr, 'returncode': result.returncode}
    except Exception as e:
        return {'error': str(e)}


def test_conversion_methods(file_path):
    """测试不同的转换方法"""
    methods = {
        'copy': ['-c', 'copy'],
        'remux': ['-c', 'copy', '-bsf:v', 'h264_mp4toannexb'],
        'reencode_fast': ['-c:v', 'libx264', '-preset', 'ultrafast', '-crf', '28'],
        'reencode_safe': ['-c:v', 'libx264', '-c:a', 'aac', '-strict', '-2'],
        'with_fixes': ['-fflags', '+genpts', '-avoid_negative_ts', 'make_zero', '-c:v', 'libx264', '-c:a', 'aac']
    }
    
    results = {}
    output_dir = file_path.parent / 'test_outputs'
    output_dir.mkdir(exist_ok=True)
    
    for method_name, params in methods.items():
        output_file = output_dir / f"{file_path.stem}_{method_name}.mp4"
        
        try:
            cmd = ['ffmpeg', '-i', str(file_path)] + params + ['-t', '10', '-y', str(output_file)]
            
            print(f"测试方法: {method_name}")
            print(f"命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                output_size = output_file.stat().st_size if output_file.exists() else 0
                results[method_name] = {
                    'success': True,
                    'output_size': output_size,
                    'command': ' '.join(cmd)
                }
                print(f"  ✅ 成功 (输出: {output_size} 字节)")
            else:
                results[method_name] = {
                    'success': False,
                    'error': result.stderr,
                    'returncode': result.returncode,
                    'command': ' '.join(cmd)
                }
                print(f"  ❌ 失败 (返回码: {result.returncode})")
            
            # 清理测试文件
            if output_file.exists():
                output_file.unlink()
                
        except Exception as e:
            results[method_name] = {
                'success': False,
                'error': str(e),
                'command': ' '.join(cmd)
            }
            print(f"  ❌ 异常: {e}")
        
        print()
    
    # 清理测试目录
    try:
        output_dir.rmdir()
    except:
        pass
    
    return results


def diagnose_file(file_path):
    """诊断单个文件"""
    print(f"诊断文件: {file_path}")
    print("=" * 60)
    
    # 检查文件基本信息
    print("1. 文件基本信息:")
    file_info = get_file_info(file_path)
    for key, value in file_info.items():
        print(f"   {key}: {value}")
    print()
    
    # 检查FFmpeg工具
    print("2. FFmpeg工具检查:")
    tools = check_ffmpeg_tools()
    for tool, available in tools.items():
        status = "✅ 可用" if available else "❌ 不可用"
        print(f"   {tool}: {status}")
    print()
    
    if not tools.get('ffprobe', False):
        print("❌ ffprobe不可用，无法进行详细分析")
        return
    
    # 使用ffprobe分析
    print("3. FFprobe 分析:")
    probe_result = probe_with_ffprobe(file_path)
    
    if 'error' in probe_result:
        print(f"   ❌ 分析失败: {probe_result['error']}")
        return
    
    # 显示格式信息
    if 'format' in probe_result:
        format_info = probe_result['format']
        print(f"   格式: {format_info.get('format_name', 'unknown')}")
        print(f"   时长: {format_info.get('duration', 'unknown')} 秒")
        print(f"   比特率: {format_info.get('bit_rate', 'unknown')} bps")
        print(f"   大小: {format_info.get('size', 'unknown')} 字节")
    
    # 显示流信息
    if 'streams' in probe_result:
        print(f"   流数量: {len(probe_result['streams'])}")
        for i, stream in enumerate(probe_result['streams']):
            codec_type = stream.get('codec_type', 'unknown')
            codec_name = stream.get('codec_name', 'unknown')
            print(f"   流 {i}: {codec_type} ({codec_name})")
    print()
    
    if not tools.get('ffmpeg', False):
        print("❌ ffmpeg不可用，无法进行转换测试")
        return
    
    # 测试转换方法
    print("4. 转换方法测试:")
    conversion_results = test_conversion_methods(file_path)
    
    successful_methods = [name for name, result in conversion_results.items() if result['success']]
    failed_methods = [name for name, result in conversion_results.items() if not result['success']]
    
    if successful_methods:
        print(f"✅ 成功的方法: {', '.join(successful_methods)}")
        print("\n推荐的转换命令:")
        for method in successful_methods[:2]:  # 显示前两个成功的方法
            cmd = conversion_results[method]['command'].replace('-t 10', '').replace('-y', '-y')
            print(f"   {method}: {cmd}")
    else:
        print("❌ 所有转换方法都失败了")
    
    if failed_methods:
        print(f"\n❌ 失败的方法: {', '.join(failed_methods)}")
        print("\n失败原因:")
        for method in failed_methods:
            result = conversion_results[method]
            error = result.get('error', '未知错误')
            returncode = result.get('returncode', 'N/A')
            print(f"   {method} (返回码 {returncode}): {error[:100]}...")


def main():
    parser = argparse.ArgumentParser(
        description="诊断TS文件转换问题",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python diagnose_ts_file.py video.ts
  python diagnose_ts_file.py /path/to/problematic/file.ts
        """
    )
    
    parser.add_argument(
        "file_path",
        help="要诊断的.ts文件路径"
    )
    
    args = parser.parse_args()
    
    file_path = Path(args.file_path)
    
    if not file_path.exists():
        print(f"❌ 错误: 文件 '{file_path}' 不存在")
        sys.exit(1)
    
    if not file_path.is_file():
        print(f"❌ 错误: '{file_path}' 不是一个文件")
        sys.exit(1)
    
    diagnose_file(file_path)


if __name__ == "__main__":
    main()
