#!/usr/bin/env python3
"""
MP4文件批量上传到阿里云OSS

功能：
1. 扫描指定目录下所有MP4文件
2. 按规则生成OSS key（video + 4级目录结构）
3. 上传到阿里云OSS
4. 生成UUID并记录到CSV文件
"""

import os
import sys
import argparse
import csv
import uuid
from pathlib import Path
import time
from typing import List, Tuple, Optional, Set, Dict
import hashlib
from datetime import datetime

try:
    import oss2
except ImportError:
    print("❌ 错误: 请先安装阿里云OSS SDK")
    print("安装命令: pip install oss2")
    sys.exit(1)


def calculate_file_hash(file_path: Path) -> str:
    """
    计算文件的MD5哈希值，用于检测文件是否已更改

    Args:
        file_path: 文件路径

    Returns:
        str: 文件的MD5哈希值
    """
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        # 分块读取，避免大文件占用过多内存
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()


def load_upload_history(csv_file: Path) -> Dict[str, Dict]:
    """
    加载上传历史记录

    Args:
        csv_file: CSV文件路径

    Returns:
        Dict: 以文件路径为key的上传记录字典
    """
    history = {}
    if not csv_file.exists():
        return history

    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                file_path = row.get('file_path', '')
                if file_path:
                    history[file_path] = {
                        'uuid': row.get('uuid', ''),
                        'filename': row.get('filename', ''),
                        'oss_key': row.get('oss_key', ''),
                        'file_size_mb': row.get('file_size_mb', ''),
                        'file_hash': row.get('file_hash', ''),
                        'upload_time': row.get('upload_time', ''),
                        'status': row.get('status', 'completed')
                    }
    except Exception as e:
        print(f"⚠️  读取上传历史失败: {e}")

    return history


def save_upload_record(csv_file: Path, record: Dict):
    """
    保存单条上传记录到CSV文件

    Args:
        csv_file: CSV文件路径
        record: 上传记录
    """
    file_exists = csv_file.exists()

    with open(csv_file, 'a', newline='', encoding='utf-8') as f:
        fieldnames = ['uuid', 'filename', 'oss_key', 'file_size_mb', 'file_hash', 'file_path', 'upload_time', 'status']
        writer = csv.DictWriter(f, fieldnames=fieldnames)

        # 如果文件不存在，写入表头
        if not file_exists:
            writer.writeheader()

        writer.writerow(record)


def is_file_uploaded(file_path: Path, history: Dict[str, Dict]) -> Tuple[bool, Dict]:
    """
    检查文件是否已经上传过，并且文件内容没有变化

    Args:
        file_path: 文件路径
        history: 上传历史记录

    Returns:
        Tuple[bool, Dict]: (是否已上传, 历史记录)
    """
    file_path_str = str(file_path)

    if file_path_str not in history:
        return False, {}

    record = history[file_path_str]

    # 检查文件是否存在
    if not file_path.exists():
        return False, record

    # 检查上传状态
    if record.get('status') != 'completed':
        return False, record

    # 检查文件哈希值是否匹配（如果有记录的话）
    if 'file_hash' in record and record['file_hash']:
        current_hash = calculate_file_hash(file_path)
        if current_hash != record['file_hash']:
            print(f"📝 文件已更改: {file_path.name}")
            return False, record

    return True, record


class OSSUploader:
    def __init__(self, access_key_id: str, access_key_secret: str, 
                 endpoint: str, bucket_name: str):
        """
        初始化OSS上传器
        
        Args:
            access_key_id: 阿里云AccessKey ID
            access_key_secret: 阿里云AccessKey Secret
            endpoint: OSS endpoint，如：https://oss-cn-hangzhou.aliyuncs.com
            bucket_name: OSS bucket名称
        """
        self.auth = oss2.Auth(access_key_id, access_key_secret)
        self.bucket = oss2.Bucket(self.auth, endpoint, bucket_name)
        self.bucket_name = bucket_name
        
    def test_connection(self) -> bool:
        """测试OSS连接"""
        try:
            # 尝试列出bucket信息
            self.bucket.get_bucket_info()
            return True
        except Exception as e:
            print(f"❌ OSS连接测试失败: {e}")
            return False
    
    def upload_file(self, local_file: Path, oss_key: str,
                   progress_callback=None, acl: str = 'private') -> bool:
        """
        上传文件到OSS

        Args:
            local_file: 本地文件路径
            oss_key: OSS对象key
            progress_callback: 进度回调函数
            acl: 访问权限，可选值：'private', 'public-read', 'public-read-write'

        Returns:
            bool: 上传是否成功
        """
        try:
            # 设置对象头信息，包括ACL权限
            headers = {
                'x-oss-object-acl': acl,
                'Content-Type': 'video/mp4'  # 设置正确的MIME类型
            }

            # 上传文件
            result = self.bucket.put_object_from_file(
                oss_key,
                str(local_file),
                headers=headers,
                progress_callback=progress_callback
            )

            # 检查上传结果
            if result.status == 200:
                return True
            else:
                print(f"❌ 上传失败，状态码: {result.status}")
                return False

        except Exception as e:
            print(f"❌ 上传异常: {e}")
            return False


def get_file_size_mb(file_path: Path) -> float:
    """获取文件大小（MB）"""
    try:
        size_bytes = file_path.stat().st_size
        return size_bytes / (1024 * 1024)
    except:
        return 0


def generate_oss_key(file_path: Path, base_dir: Path) -> str:
    """
    生成OSS key
    规则：video + 文件所在目录往上4级目录结构

    Args:
        file_path: 文件完整路径
        base_dir: 基础目录路径

    Returns:
        str: OSS key
    """
    # 获取相对于基础目录的路径
    try:
        relative_path = file_path.relative_to(base_dir)
        # 获取目录部分（不包括文件名）
        relative_dir_parts = relative_path.parent.parts

        # 如果文件直接在基础目录下，使用基础目录的最后几级作为目录结构
        if len(relative_dir_parts) == 0:
            # 使用基础目录的最后4级作为目录结构
            base_dir_parts = base_dir.parts
            dir_parts = base_dir_parts[-4:] if len(base_dir_parts) > 4 else base_dir_parts
        else:
            # 组合基础目录的部分层级 + 相对目录
            base_dir_parts = base_dir.parts
            # 取基础目录的最后2级 + 相对目录的所有层级，总共不超过4级
            base_levels = base_dir_parts[-2:] if len(base_dir_parts) >= 2 else base_dir_parts
            combined_parts = base_levels + relative_dir_parts
            dir_parts = combined_parts[-4:] if len(combined_parts) > 4 else combined_parts

    except ValueError:
        # 如果文件不在基础目录下，使用绝对路径的最后4级目录
        dir_parts = file_path.parent.parts
        dir_parts = dir_parts[-4:] if len(dir_parts) > 4 else dir_parts

    # 清理目录部分，移除空字符串和根目录标识
    dir_parts = [part for part in dir_parts if part and part != '/']

    # 确保我们有足够的目录层级
    if len(dir_parts) == 0:
        # 如果没有目录层级，使用文件名作为目录
        dir_parts = [file_path.stem]

    # 构建OSS key：video + 目录结构 + 文件名
    oss_key_parts = ["video"] + list(dir_parts) + [file_path.name]
    oss_key = "/".join(oss_key_parts)

    return oss_key


def find_mp4_files(directory: Path) -> List[Path]:
    """
    查找目录下所有MP4文件
    
    Args:
        directory: 搜索目录
        
    Returns:
        List[Path]: MP4文件列表
    """
    mp4_files = []
    
    # 递归查找所有.mp4文件
    all_mp4_files = list(directory.rglob("*.mp4"))
    
    for mp4_file in all_mp4_files:
        # 跳过系统文件
        if mp4_file.name.startswith('._'):
            continue
            
        # 跳过空文件或过小的文件（小于1MB）
        try:
            if mp4_file.stat().st_size < 1024 * 1024:
                continue
        except:
            continue
            
        mp4_files.append(mp4_file)
    
    return mp4_files


def create_progress_callback(file_name: str, file_size_mb: float):
    """创建上传进度回调函数"""
    def progress_callback(consumed_bytes, total_bytes):
        if total_bytes:
            percentage = (consumed_bytes / total_bytes) * 100
            consumed_mb = consumed_bytes / (1024 * 1024)
            print(f"    📤 上传进度: {percentage:.1f}% ({consumed_mb:.1f}/{file_size_mb:.1f} MB)", end='\r')
    
    return progress_callback


def upload_mp4_files(directory_path: str, access_key_id: str, access_key_secret: str,
                    endpoint: str, bucket_name: str, csv_file: str = "oss_files.csv",
                    dry_run: bool = False, overwrite: bool = False,
                    acl: str = 'private', resume: bool = True) -> Tuple[int, int, List[str]]:
    """
    批量上传MP4文件到OSS

    Args:
        directory_path: 本地目录路径
        access_key_id: 阿里云AccessKey ID
        access_key_secret: 阿里云AccessKey Secret
        endpoint: OSS endpoint
        bucket_name: OSS bucket名称
        csv_file: CSV记录文件名
        dry_run: 预览模式
        overwrite: 是否覆盖已存在的OSS对象
        acl: 访问权限，可选值：'private', 'public-read', 'public-read-write'
        resume: 是否启用断点续传（跳过已上传的文件）

    Returns:
        Tuple[int, int, List[str]]: (成功数量, 失败数量, 错误列表)
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        return 0, 1, [f"目录 '{directory_path}' 不存在"]
    
    if not directory.is_dir():
        return 0, 1, [f"'{directory_path}' 不是一个目录"]
    
    # 查找所有MP4文件
    mp4_files = find_mp4_files(directory)

    if not mp4_files:
        print(f"❌ 在目录 '{directory_path}' 中没有找到任何有效的 .mp4 文件")
        return 0, 0, []

    print(f"📁 找到 {len(mp4_files)} 个有效的 .mp4 文件")

    # 加载上传历史记录
    csv_path = Path(csv_file)
    upload_history = load_upload_history(csv_path) if resume else {}

    # 过滤已上传的文件
    files_to_upload = []
    skipped_files = []

    for mp4_file in mp4_files:
        if resume:
            is_uploaded, record = is_file_uploaded(mp4_file, upload_history)
            if is_uploaded and not overwrite:
                skipped_files.append((mp4_file, record))
                continue

        files_to_upload.append(mp4_file)

    if skipped_files:
        print(f"⏭️  跳过 {len(skipped_files)} 个已上传的文件")
        for file_path, record in skipped_files[:3]:  # 只显示前3个
            print(f"   📄 {file_path.name} (已于 {record.get('upload_time', '未知时间')} 上传)")
        if len(skipped_files) > 3:
            print(f"   ... 还有 {len(skipped_files) - 3} 个文件")

    if not files_to_upload:
        print("✅ 所有文件都已上传完成")
        return len(skipped_files), 0, []

    print(f"📤 需要上传 {len(files_to_upload)} 个文件")

    # 计算需要上传的文件总大小
    total_size_mb = sum(get_file_size_mb(mp4_file) for mp4_file in files_to_upload)
    print(f"📊 待上传文件大小: {total_size_mb:.1f} MB")
    
    if dry_run:
        print("🔍 预览模式 - 显示将要上传的文件和OSS key")
        print("-" * 80)

        for i, mp4_file in enumerate(files_to_upload, 1):
            file_size_mb = get_file_size_mb(mp4_file)
            oss_key = generate_oss_key(mp4_file, directory)
            file_uuid = str(uuid.uuid4())

            print(f"[{i}/{len(files_to_upload)}] 📹 {mp4_file.name} ({file_size_mb:.1f} MB)")
            print(f"  UUID: {file_uuid}")
            print(f"  OSS Key: {oss_key}")
            print(f"  本地路径: {mp4_file}")
            print()

        # 创建预览CSV记录
        preview_records = []
        for mp4_file in files_to_upload:
            file_size_mb = get_file_size_mb(mp4_file)
            oss_key = generate_oss_key(mp4_file, directory)
            file_hash = calculate_file_hash(mp4_file)

            record = {
                'uuid': str(uuid.uuid4()),
                'filename': mp4_file.name,
                'oss_key': oss_key,
                'file_size_mb': f"{file_size_mb:.2f}",
                'file_hash': file_hash,
                'file_path': str(mp4_file),
                'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'preview'
            }
            preview_records.append(record)

        return len(files_to_upload), 0, []
    
    # 初始化OSS上传器
    print("🔗 连接阿里云OSS...")
    uploader = OSSUploader(access_key_id, access_key_secret, endpoint, bucket_name)
    
    if not uploader.test_connection():
        return 0, 1, ["OSS连接失败"]
    
    print(f"✅ OSS连接成功 (Bucket: {bucket_name})")
    print("-" * 80)
    
    # 准备CSV文件
    csv_path = Path(csv_file)
    csv_exists = csv_path.exists()
    
    success_count = 0
    error_count = 0
    errors = []
    uploaded_size_mb = 0
    
    # 打开CSV文件进行写入
    with open(csv_path, 'a', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['uuid', 'filename', 'oss_key', 'file_size_mb', 'file_hash', 'file_path', 'upload_time', 'status']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        # 如果是新文件，写入表头
        if not csv_exists:
            writer.writeheader()
        
        for i, mp4_file in enumerate(files_to_upload, 1):
            file_size_mb = get_file_size_mb(mp4_file)
            oss_key = generate_oss_key(mp4_file, directory)
            file_uuid = str(uuid.uuid4())
            file_hash = calculate_file_hash(mp4_file)

            print(f"[{i}/{len(files_to_upload)}] 📹 {mp4_file.name} ({file_size_mb:.1f} MB)")
            print(f"  UUID: {file_uuid}")
            print(f"  OSS Key: {oss_key}")
            print(f"  🔍 计算文件哈希: {file_hash[:8]}...")
            
            # 检查OSS中是否已存在
            if not overwrite:
                try:
                    uploader.bucket.head_object(oss_key)
                    print(f"  ⏭️  跳过: OSS中已存在该文件")
                    error_count += 1
                    errors.append(f"OSS中已存在: {oss_key}")
                    continue
                except oss2.exceptions.NoSuchKey:
                    # 文件不存在，可以上传
                    pass
                except Exception as e:
                    print(f"  ❌ 检查文件存在性失败: {e}")
                    error_count += 1
                    errors.append(f"检查失败 {mp4_file.name}: {e}")
                    continue
            
            # 创建进度回调
            progress_callback = create_progress_callback(mp4_file.name, file_size_mb)
            
            # 上传文件
            start_time = time.time()
            success = uploader.upload_file(mp4_file, oss_key, progress_callback, acl)
            upload_time = time.time() - start_time
            
            print()  # 换行，清除进度显示
            
            # 准备记录数据
            record = {
                'uuid': file_uuid,
                'filename': mp4_file.name,
                'oss_key': oss_key,
                'file_size_mb': f"{file_size_mb:.2f}",
                'file_hash': file_hash,
                'file_path': str(mp4_file),
                'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'status': 'completed' if success else 'failed'
            }

            if success:
                print(f"  ✅ 上传成功: {upload_time:.1f}s")
                success_count += 1
                uploaded_size_mb += file_size_mb
            else:
                print(f"  ❌ 上传失败")
                errors.append(f"上传失败: {mp4_file.name}")
                error_count += 1

            # 写入CSV记录（无论成功还是失败都记录）
            writer.writerow(record)
            
            # 显示总体进度
            progress = (uploaded_size_mb / total_size_mb * 100) if total_size_mb > 0 else 0
            print(f"  📈 总进度: {progress:.1f}% ({uploaded_size_mb:.1f}/{total_size_mb:.1f} MB)")
            print()
    
    return success_count, error_count, errors


def main():
    parser = argparse.ArgumentParser(
        description="MP4文件批量上传到阿里云OSS",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
OSS Key生成规则:
  video + 文件所在目录往上4级 + 文件名
  
示例:
  本地文件: /data/videos/2023/math/grade8/lesson1/video.mp4
  OSS Key: video/2023/math/grade8/lesson1/video.mp4

环境变量配置 (推荐):
  export OSS_ACCESS_KEY_ID="your_access_key_id"
  export OSS_ACCESS_KEY_SECRET="your_access_key_secret"
  export OSS_ENDPOINT="https://oss-cn-hangzhou.aliyuncs.com"
  export OSS_BUCKET_NAME="your_bucket_name"

使用示例:
  python upload_mp4_to_oss.py /path/to/videos --dry-run
  python upload_mp4_to_oss.py /path/to/videos --csv-file my_uploads.csv
        """
    )
    
    parser.add_argument(
        "directory",
        help="要上传的本地目录路径"
    )
    
    parser.add_argument(
        "--access-key-id",
        help="阿里云AccessKey ID (或设置环境变量 OSS_ACCESS_KEY_ID)"
    )
    
    parser.add_argument(
        "--access-key-secret",
        help="阿里云AccessKey Secret (或设置环境变量 OSS_ACCESS_KEY_SECRET)"
    )
    
    parser.add_argument(
        "--endpoint",
        help="OSS endpoint (或设置环境变量 OSS_ENDPOINT)"
    )
    
    parser.add_argument(
        "--bucket-name",
        help="OSS bucket名称 (或设置环境变量 OSS_BUCKET_NAME)"
    )
    
    parser.add_argument(
        "--csv-file",
        default="oss_files.csv",
        help="CSV记录文件名 (默认: oss_files.csv)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，只显示将要上传的文件，不实际上传"
    )
    
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="覆盖OSS中已存在的文件"
    )

    parser.add_argument(
        "--acl",
        choices=['private', 'public-read', 'public-read-write'],
        default='private',
        help="文件访问权限 (默认: private)"
    )

    parser.add_argument(
        "--no-resume",
        action="store_true",
        help="禁用断点续传，重新上传所有文件"
    )
    
    args = parser.parse_args()
    
    # 获取OSS配置（优先使用命令行参数，其次使用环境变量）
    access_key_id = args.access_key_id or os.getenv('OSS_ACCESS_KEY_ID')
    access_key_secret = args.access_key_secret or os.getenv('OSS_ACCESS_KEY_SECRET')
    endpoint = args.endpoint or os.getenv('OSS_ENDPOINT')
    bucket_name = args.bucket_name or os.getenv('OSS_BUCKET_NAME')
    
    # 检查必需的配置（预览模式除外）
    if not args.dry_run:
        missing_configs = []
        if not access_key_id:
            missing_configs.append("access-key-id (或环境变量 OSS_ACCESS_KEY_ID)")
        if not access_key_secret:
            missing_configs.append("access-key-secret (或环境变量 OSS_ACCESS_KEY_SECRET)")
        if not endpoint:
            missing_configs.append("endpoint (或环境变量 OSS_ENDPOINT)")
        if not bucket_name:
            missing_configs.append("bucket-name (或环境变量 OSS_BUCKET_NAME)")

        if missing_configs:
            print("❌ 缺少必需的OSS配置:")
            for config in missing_configs:
                print(f"  - {config}")
            print("\n请通过命令行参数或环境变量提供这些配置")
            print("💡 提示: 使用 --dry-run 可以在不配置OSS的情况下预览文件")
            sys.exit(1)
    else:
        # 预览模式使用默认值
        if not bucket_name:
            bucket_name = "your-bucket-name"
    
    directory_path = os.path.abspath(args.directory)
    
    print("=" * 80)
    print("🚀 MP4文件批量上传到阿里云OSS")
    print("=" * 80)
    print(f"📂 本地目录: {directory_path}")
    print(f"🪣 OSS Bucket: {bucket_name}")
    print(f"📝 CSV文件: {args.csv_file}")
    
    if args.dry_run:
        print("🔍 运行模式: 预览模式")
    else:
        print("🚀 运行模式: 实际上传")
        if args.overwrite:
            print("⚠️  覆盖模式: 启用")

    print(f"🔒 访问权限: {args.acl}")

    if not args.no_resume:
        print("🔄 断点续传: 启用")
    else:
        print("🔄 断点续传: 禁用")
    
    print("=" * 80)
    
    # 执行上传
    start_time = time.time()
    success_count, error_count, errors = upload_mp4_files(
        directory_path,
        access_key_id,
        access_key_secret,
        endpoint,
        bucket_name,
        args.csv_file,
        args.dry_run,
        args.overwrite,
        args.acl,
        not args.no_resume  # resume = not no_resume
    )
    total_time = time.time() - start_time
    
    print("=" * 80)
    print(f"🏁 处理完成 (总耗时: {total_time:.1f}秒):")
    print(f"  ✅ 成功: {success_count} 个文件")
    print(f"  ❌ 失败: {error_count} 个文件")
    
    if success_count > 0:
        avg_time = total_time / success_count if success_count > 0 else 0
        print(f"  ⚡ 平均上传速度: {avg_time:.1f}秒/文件")
        print(f"  📝 记录已保存到: {args.csv_file}")
    
    if errors:
        print(f"\n❌ 失败文件列表:")
        for i, error in enumerate(errors, 1):
            print(f"  {i}. {error}")
    
    sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
