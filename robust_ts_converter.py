#!/usr/bin/env python3
"""
强化版TS转MP4转换器

专门处理有问题的.ts文件，使用多种策略尝试转换
"""

import os
import sys
import argparse
import subprocess
import json
from pathlib import Path
import time


def check_ffmpeg():
    """检查FFmpeg是否可用"""
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        return result.returncode == 0
    except:
        return False


def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    try:
        size_bytes = file_path.stat().st_size
        return size_bytes / (1024 * 1024)
    except:
        return 0


def try_conversion_strategies(input_file, output_file, timeout=300):
    """
    尝试多种转换策略
    
    Args:
        input_file (Path): 输入文件
        output_file (Path): 输出文件
        timeout (int): 每次尝试的超时时间（秒）
    
    Returns:
        tuple: (success, method_used, error_message)
    """
    
    # 定义多种转换策略，按成功率排序
    strategies = [
        {
            'name': 'copy_stream',
            'description': '直接复制流（最快）',
            'params': ['-c', 'copy', '-avoid_negative_ts', 'make_zero']
        },
        {
            'name': 'remux_with_bsf',
            'description': '重新封装并修复比特流',
            'params': ['-c', 'copy', '-bsf:v', 'h264_mp4toannexb', '-avoid_negative_ts', 'make_zero']
        },
        {
            'name': 'fix_timestamps',
            'description': '修复时间戳问题',
            'params': ['-fflags', '+genpts', '-avoid_negative_ts', 'make_zero', '-c', 'copy']
        },
        {
            'name': 'ignore_errors',
            'description': '忽略错误继续处理',
            'params': ['-err_detect', 'ignore_err', '-c', 'copy', '-avoid_negative_ts', 'make_zero']
        },
        {
            'name': 'force_format',
            'description': '强制指定输入格式',
            'params': ['-f', 'mpegts', '-c', 'copy', '-avoid_negative_ts', 'make_zero']
        },
        {
            'name': 'concat_demuxer',
            'description': '使用concat解复用器',
            'params': ['-f', 'concat', '-safe', '0', '-c', 'copy']
        },
        {
            'name': 'reencode_h264',
            'description': '重新编码为H.264',
            'params': ['-c:v', 'libx264', '-c:a', 'aac', '-preset', 'fast', '-crf', '23']
        },
        {
            'name': 'reencode_with_fixes',
            'description': '重新编码并修复问题',
            'params': [
                '-fflags', '+genpts', '-avoid_negative_ts', 'make_zero',
                '-c:v', 'libx264', '-c:a', 'aac', '-preset', 'fast', '-crf', '23',
                '-movflags', '+faststart'
            ]
        },
        {
            'name': 'segment_and_concat',
            'description': '分段处理后合并',
            'params': ['-c:v', 'libx264', '-c:a', 'aac', '-f', 'segment', '-segment_time', '60']
        },
        {
            'name': 'raw_copy',
            'description': '原始数据复制',
            'params': ['-c:v', 'copy', '-c:a', 'copy', '-f', 'mp4', '-movflags', '+faststart']
        }
    ]
    
    for strategy in strategies:
        print(f"  尝试策略: {strategy['name']} - {strategy['description']}")
        
        try:
            # 特殊处理concat策略
            if strategy['name'] == 'concat_demuxer':
                # 创建临时文件列表
                concat_file = input_file.parent / f"temp_concat_{input_file.stem}.txt"
                with open(concat_file, 'w') as f:
                    f.write(f"file '{input_file}'\n")
                
                cmd = ['ffmpeg', '-f', 'concat', '-safe', '0', '-i', str(concat_file)]
                cmd.extend(['-c', 'copy', '-y', str(output_file)])
                
                # 清理函数
                def cleanup():
                    if concat_file.exists():
                        concat_file.unlink()
            
            elif strategy['name'] == 'segment_and_concat':
                # 分段策略需要特殊处理
                segment_pattern = input_file.parent / f"temp_segment_{input_file.stem}_%03d.mp4"
                cmd = ['ffmpeg', '-i', str(input_file)]
                cmd.extend(strategy['params'])
                cmd.extend(['-y', str(segment_pattern)])
                
                def cleanup():
                    # 清理分段文件
                    for segment in input_file.parent.glob(f"temp_segment_{input_file.stem}_*.mp4"):
                        segment.unlink()
            
            else:
                cmd = ['ffmpeg', '-i', str(input_file)]
                cmd.extend(strategy['params'])
                cmd.extend(['-y', str(output_file)])
                cleanup = lambda: None
            
            # 执行命令
            result = subprocess.run(cmd, 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=timeout)
            
            # 清理临时文件
            cleanup()
            
            if result.returncode == 0 and output_file.exists() and output_file.stat().st_size > 0:
                print(f"    ✅ 成功！")
                return True, strategy['name'], None
            else:
                error_msg = result.stderr if result.stderr else f"返回码: {result.returncode}"
                print(f"    ❌ 失败: {error_msg[:100]}...")
                
        except subprocess.TimeoutExpired:
            cleanup()
            print(f"    ❌ 超时（{timeout}秒）")
        except Exception as e:
            cleanup()
            print(f"    ❌ 异常: {str(e)}")
    
    return False, None, "所有转换策略都失败了"


def convert_problematic_ts(directory_path, dry_run=False, overwrite=False, timeout=300):
    """
    转换有问题的TS文件
    
    Args:
        directory_path (str): 目录路径
        dry_run (bool): 预览模式
        overwrite (bool): 覆盖已存在文件
        timeout (int): 每个文件的转换超时时间
    
    Returns:
        tuple: (success_count, error_count, errors)
    """
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"错误: 目录 '{directory_path}' 不存在")
        return 0, 1, [f"目录 '{directory_path}' 不存在"]
    
    if not directory.is_dir():
        print(f"错误: '{directory_path}' 不是一个目录")
        return 0, 1, [f"'{directory_path}' 不是一个目录"]
    
    if not check_ffmpeg():
        error_msg = "错误: 系统中未找到ffmpeg"
        print(error_msg)
        return 0, 1, [error_msg]
    
    # 查找所有.ts文件，过滤掉系统文件和空文件
    all_ts_files = list(directory.rglob("*.ts"))
    ts_files = []

    for ts_file in all_ts_files:
        # 跳过macOS元数据文件
        if ts_file.name.startswith('._'):
            continue

        # 跳过空文件或过小的文件（小于1KB）
        try:
            if ts_file.stat().st_size < 1024:
                continue
        except:
            continue

        ts_files.append(ts_file)

    if not ts_files:
        print(f"在目录 '{directory_path}' 中没有找到任何有效的 .ts 文件")
        if all_ts_files:
            print(f"发现 {len(all_ts_files)} 个 .ts 文件，但都被过滤掉了（系统文件或空文件）")
        return 0, 0, []
    
    print(f"找到 {len(ts_files)} 个 .ts 文件")
    print(f"转换超时设置: {timeout} 秒/文件")
    print("-" * 60)
    
    success_count = 0
    error_count = 0
    errors = []
    
    for i, ts_file in enumerate(ts_files, 1):
        file_size_mb = get_file_size_mb(ts_file)
        mp4_file = ts_file.with_suffix('.mp4')
        
        print(f"[{i}/{len(ts_files)}] 处理: {ts_file.name} ({file_size_mb:.1f} MB)")
        
        if dry_run:
            print(f"  [预览] {ts_file} -> {mp4_file}")
            success_count += 1
            continue
        
        # 检查目标文件是否存在
        if mp4_file.exists() and not overwrite:
            print(f"  跳过: 目标文件已存在 {mp4_file.name}")
            error_count += 1
            errors.append(f"目标文件已存在: {mp4_file}")
            continue
        
        print(f"  正在转换: {ts_file.name} -> {mp4_file.name}")
        start_time = time.time()
        
        # 尝试转换
        success, method, error = try_conversion_strategies(ts_file, mp4_file, timeout)
        
        elapsed_time = time.time() - start_time
        
        if success:
            output_size_mb = get_file_size_mb(mp4_file)
            print(f"  ✅ 转换成功！方法: {method}, 耗时: {elapsed_time:.1f}s, 输出: {output_size_mb:.1f} MB")
            success_count += 1
        else:
            print(f"  ❌ 转换失败: {error}")
            errors.append(f"{ts_file.name}: {error}")
            error_count += 1
        
        print()
    
    return success_count, error_count, errors


def main():
    parser = argparse.ArgumentParser(
        description="强化版TS转MP4转换器 - 专门处理有问题的.ts文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
这个工具使用多种策略尝试转换有问题的.ts文件：
1. 直接复制流
2. 重新封装并修复比特流
3. 修复时间戳问题
4. 忽略错误继续处理
5. 强制指定输入格式
6. 重新编码
7. 其他修复策略

示例:
  python robust_ts_converter.py /path/to/videos
  python robust_ts_converter.py /path/to/videos --dry-run
  python robust_ts_converter.py /path/to/videos --timeout 600
        """
    )
    
    parser.add_argument(
        "directory",
        help="要处理的目录路径"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="预览模式，只显示将要转换的文件"
    )
    
    parser.add_argument(
        "--overwrite",
        action="store_true",
        help="覆盖已存在的.mp4文件"
    )
    
    parser.add_argument(
        "--timeout",
        type=int,
        default=300,
        help="每个文件的转换超时时间（秒，默认300）"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="显示详细信息"
    )
    
    args = parser.parse_args()
    
    directory_path = os.path.abspath(args.directory)
    
    print("=" * 60)
    print("强化版 TS to MP4 转换器")
    print("=" * 60)
    print(f"处理目录: {directory_path}")
    
    if args.dry_run:
        print("运行模式: 预览模式")
    else:
        print("运行模式: 实际转换")
        if args.overwrite:
            print("覆盖模式: 启用")
    
    print("=" * 60)
    
    # 执行转换
    start_time = time.time()
    success_count, error_count, errors = convert_problematic_ts(
        directory_path,
        dry_run=args.dry_run,
        overwrite=args.overwrite,
        timeout=args.timeout
    )
    total_time = time.time() - start_time
    
    print("=" * 60)
    print(f"处理完成 (总耗时: {total_time:.1f}秒):")
    print(f"  ✅ 成功: {success_count} 个文件")
    print(f"  ❌ 失败: {error_count} 个文件")
    
    if errors and args.verbose:
        print("\n错误详情:")
        for error in errors:
            print(f"  - {error}")
    
    if error_count > 0:
        print(f"\n建议:")
        print(f"  1. 检查失败的文件是否损坏")
        print(f"  2. 尝试增加超时时间: --timeout 600")
        print(f"  3. 使用诊断工具: python diagnose_ts_file.py <文件路径>")
    
    sys.exit(0 if error_count == 0 else 1)


if __name__ == "__main__":
    main()
