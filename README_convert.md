# TS to MP4 Converter

这个工具可以递归地将指定目录下所有的 `.ts` 文件转换为 `.mp4` 文件，使用 FFmpeg 进行高质量的视频转换。

## 功能特点

- 递归搜索指定目录及其所有子目录
- 批量转换所有 `.ts` 文件为 `.mp4` 文件
- 支持多种质量设置（copy、high、medium、low）
- 支持预览模式，可以先查看将要转换的文件
- 安全检查：如果目标文件已存在，可选择跳过或覆盖
- 详细的进度显示和转换统计
- 可选择在转换成功后删除源文件
- 详细的错误处理和日志输出
- 支持命令行参数

## 系统要求

- Python 3.6 或更高版本
- FFmpeg（必须安装并在系统PATH中可用）

### 安装 FFmpeg

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install ffmpeg
```

**Windows:**
1. 从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载
2. 解压并将 `bin` 目录添加到系统 PATH

## 使用方法

### 基本用法

```bash
# 转换当前目录下所有 .ts 文件
python convert_ts_to_mp4.py .

# 转换指定目录下所有 .ts 文件
python convert_ts_to_mp4.py /path/to/your/videos
```

### 预览模式

在实际转换之前，可以使用预览模式查看将要转换的文件：

```bash
# 预览模式 - 只显示将要转换的文件，不实际执行
python convert_ts_to_mp4.py /path/to/videos --dry-run
```

### 质量设置

```bash
# 高质量转换（文件较大，质量最好）
python convert_ts_to_mp4.py /path/to/videos --quality high

# 中等质量转换（默认，平衡质量和文件大小）
python convert_ts_to_mp4.py /path/to/videos --quality medium

# 低质量转换（文件较小，质量较低）
python convert_ts_to_mp4.py /path/to/videos --quality low

# 直接复制流（最快，但可能有兼容性问题）
python convert_ts_to_mp4.py /path/to/videos --quality copy
```

### 高级选项

```bash
# 覆盖已存在的 .mp4 文件
python convert_ts_to_mp4.py /path/to/videos --overwrite

# 转换成功后删除源 .ts 文件（谨慎使用！）
python convert_ts_to_mp4.py /path/to/videos --delete-source

# 显示详细的错误信息
python convert_ts_to_mp4.py /path/to/videos --verbose

# 组合使用多个选项
python convert_ts_to_mp4.py /path/to/videos --quality high --overwrite --verbose
```

## 命令行参数

- `directory`: 要处理的目录路径（必需）
- `--dry-run`: 预览模式，只显示将要转换的文件
- `--quality`, `-q`: 转换质量（copy/high/medium/low，默认：medium）
- `--overwrite`: 覆盖已存在的 .mp4 文件
- `--delete-source`: 转换成功后删除源 .ts 文件
- `--verbose`, `-v`: 显示详细信息，包括错误详情

## 质量设置说明

| 质量 | 视频编码 | 音频编码 | 特点 |
|------|----------|----------|------|
| copy | 直接复制 | 直接复制 | 最快，保持原始质量，但可能有兼容性问题 |
| high | H.264 CRF 18 | AAC 192k | 高质量，文件较大 |
| medium | H.264 CRF 23 | AAC 128k | 平衡质量和大小（推荐） |
| low | H.264 CRF 28 | AAC 96k | 较小文件，质量较低 |

## 示例输出

### 正常转换
```
============================================================
TS to MP4 转换器
============================================================
处理目录: /Users/<USER>/Videos
转换质量: medium
运行模式: 实际转换
============================================================
找到 3 个 .ts 文件
总文件大小: 150.5 MB
转换质量: medium
------------------------------------------------------------
[1/3] 处理: video1.ts (50.2 MB)
  正在转换: video1.ts -> video1.mp4
  ✅ 转换成功: 45.3s, 输出: 48.1 MB, 压缩率: 4.2%
  进度: 33.3% (50.2/150.5 MB)

[2/3] 处理: video2.ts (75.8 MB)
  正在转换: video2.ts -> video2.mp4
  ✅ 转换成功: 68.7s, 输出: 72.3 MB, 压缩率: 4.6%
  进度: 83.7% (126.0/150.5 MB)

[3/3] 处理: video3.ts (24.5 MB)
  正在转换: video3.ts -> video3.mp4
  ✅ 转换成功: 22.1s, 输出: 23.8 MB, 压缩率: 2.9%
  进度: 100.0% (150.5/150.5 MB)

============================================================
处理完成 (耗时: 136.1秒):
  ✅ 成功: 3 个文件
  ❌ 失败: 0 个文件
```

### 预览模式
```
============================================================
TS to MP4 转换器
============================================================
处理目录: /Users/<USER>/Videos
转换质量: medium
运行模式: 预览模式 (不会实际转换文件)
============================================================
找到 3 个 .ts 文件
总文件大小: 150.5 MB
转换质量: medium
------------------------------------------------------------
[1/3] 处理: video1.ts (50.2 MB)
  [预览] /Users/<USER>/Videos/video1.ts -> /Users/<USER>/Videos/video1.mp4
  进度: 33.3% (50.2/150.5 MB)

[2/3] 处理: video2.ts (75.8 MB)
  [预览] /Users/<USER>/Videos/video2.ts -> /Users/<USER>/Videos/video2.mp4
  进度: 83.7% (126.0/150.5 MB)

[3/3] 处理: video3.ts (24.5 MB)
  [预览] /Users/<USER>/Videos/video3.ts -> /Users/<USER>/Videos/video3.mp4
  进度: 100.0% (150.5/150.5 MB)

============================================================
处理完成 (耗时: 0.1秒):
  ✅ 成功: 3 个文件
  ❌ 失败: 0 个文件
```

## 安全特性

1. **文件存在检查**: 如果目标 `.mp4` 文件已存在，默认会跳过转换以避免覆盖
2. **目录验证**: 检查指定的路径是否存在且为目录
3. **FFmpeg检查**: 在开始转换前检查FFmpeg是否可用
4. **错误处理**: 捕获并报告所有转换过程中的错误
5. **预览模式**: 可以先预览要转换的文件，确认无误后再执行
6. **删除确认**: 使用 `--delete-source` 时会要求用户确认

## 注意事项

- 请确保有足够的磁盘空间存储转换后的文件
- 转换过程可能需要较长时间，特别是大文件或高质量设置
- 建议先使用 `--dry-run` 参数预览要转换的文件
- 使用 `--delete-source` 选项时要特别小心，建议先备份重要文件
- 如果转换失败，检查FFmpeg是否正确安装并在PATH中可用
- 某些特殊格式的.ts文件可能需要特定的转换参数

## 故障排除

### FFmpeg 不可用
```
错误: 系统中未找到ffmpeg，请先安装ffmpeg
```
解决方法：按照上述说明安装FFmpeg

### 转换失败
- 检查源文件是否损坏
- 尝试使用 `--quality copy` 选项
- 查看详细错误信息（使用 `--verbose`）

### 磁盘空间不足
- 清理磁盘空间
- 考虑使用更低的质量设置
- 分批处理文件
