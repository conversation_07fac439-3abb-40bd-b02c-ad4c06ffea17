#!/bin/bash

# PDF上传工具环境配置脚本

echo "📚 PDF文件上传到阿里云OSS工具 - 环境配置"
echo "================================================"

# 检查Python环境
echo "🐍 检查Python环境..."
if command -v python3 &> /dev/null; then
    echo "✅ Python3 已安装: $(python3 --version)"
else
    echo "❌ 请先安装Python3"
    exit 1
fi

# 检查pip
if command -v pip3 &> /dev/null; then
    echo "✅ pip3 已安装"
else
    echo "❌ 请先安装pip3"
    exit 1
fi

# 安装依赖
echo ""
echo "📦 安装依赖包..."
pip3 install oss2

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 环境变量配置提示
echo ""
echo "⚙️ 环境变量配置"
echo "请设置以下环境变量（替换为你的实际值）:"
echo ""
echo "export OSS_ACCESS_KEY_ID=\"your_access_key_id\""
echo "export OSS_ACCESS_KEY_SECRET=\"your_access_key_secret\""
echo "export OSS_ENDPOINT=\"oss-cn-beijing.aliyuncs.com\"  # 根据你的区域调整"
echo "export OSS_BUCKET_NAME=\"your_bucket_name\""
echo ""

# 检查当前环境变量
echo "🔍 检查当前环境变量:"
vars=("OSS_ACCESS_KEY_ID" "OSS_ACCESS_KEY_SECRET" "OSS_ENDPOINT" "OSS_BUCKET_NAME")
all_set=true

for var in "${vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ $var: 未设置"
        all_set=false
    else
        # 隐藏敏感信息
        if [[ $var == *"SECRET"* ]] || [[ $var == *"KEY"* ]]; then
            value="${!var}"
            display_value="${value:0:8}..."
        else
            display_value="${!var}"
        fi
        echo "✅ $var: $display_value"
    fi
done

echo ""
if [ "$all_set" = true ]; then
    echo "✅ 环境配置完整！"
    echo ""
    echo "🚀 现在可以使用PDF上传工具:"
    echo "   python3 upload_pdf_to_oss.py /path/to/pdf/directory"
    echo ""
    echo "📖 更多使用方法请查看: pdf_upload_example.md"
else
    echo "⚠️ 请设置缺失的环境变量后再使用工具"
    echo ""
    echo "💡 可以将环境变量添加到 ~/.bashrc 或 ~/.zshrc 文件中:"
    echo "   echo 'export OSS_ACCESS_KEY_ID=\"your_key\"' >> ~/.bashrc"
    echo "   source ~/.bashrc"
fi

echo ""
echo "🧪 运行测试:"
echo "   python3 test_pdf_upload.py"
echo ""
echo "================================================"
