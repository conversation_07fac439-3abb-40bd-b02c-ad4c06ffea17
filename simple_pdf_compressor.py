#!/usr/bin/env python3
"""
简化版PDF压缩工具

专注于实际可用的压缩功能，目标是将PDF压缩到原来的20%左右大小
"""

import os
import sys
import argparse
from pathlib import Path
from typing import Tuple
import tempfile
import shutil
import subprocess

try:
    import PyPDF2
    from PyPDF2 import PdfReader, PdfWriter
except ImportError:
    print("❌ 请安装PyPDF2: pip install PyPDF2")
    sys.exit(1)

class SimplePDFCompressor:
    def __init__(self):
        self.temp_dir = None
    
    def __enter__(self):
        self.temp_dir = tempfile.mkdtemp(prefix="pdf_compress_")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.temp_dir and Path(self.temp_dir).exists():
            shutil.rmtree(self.temp_dir)
    
    def get_file_size_mb(self, file_path: Path) -> float:
        """获取文件大小（MB）"""
        return file_path.stat().st_size / (1024 * 1024)
    
    def compress_with_pypdf2(self, input_path: Path, output_path: Path) -> bool:
        """使用PyPDF2进行基础压缩"""
        try:
            reader = PdfReader(str(input_path))
            writer = PdfWriter()
            
            # 复制页面并压缩
            for page in reader.pages:
                # 压缩内容流
                if hasattr(page, 'compress_content_streams'):
                    page.compress_content_streams()
                writer.add_page(page)
            
            # 移除链接和元数据
            if hasattr(writer, 'remove_links'):
                writer.remove_links()
            elif hasattr(writer, 'removeLinks'):
                writer.removeLinks()
            
            # 保存
            with open(output_path, 'wb') as f:
                writer.write(f)
            
            return True
            
        except Exception as e:
            print(f"   ❌ PyPDF2压缩失败: {e}")
            return False
    
    def compress_with_ghostscript(self, input_path: Path, output_path: Path,
                                 quality: str = "ebook") -> bool:
        """使用Ghostscript进行高级压缩"""
        try:
            # 检查Ghostscript是否可用
            gs_command = "gs"
            if os.name == 'nt':  # Windows
                gs_command = "gswin64c"

            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # 根据质量级别设置更激进的参数
            if quality == "ultra_screen":
                # 超级极限压缩设置 - 目标20%压缩比
                cmd = [
                    gs_command,
                    "-sDEVICE=pdfwrite",
                    "-dCompatibilityLevel=1.4",
                    "-dPDFSETTINGS=/screen",
                    "-dNOPAUSE",
                    "-dQUIET",
                    "-dBATCH",
                    "-dColorImageResolution=50",      # 极低分辨率
                    "-dGrayImageResolution=50",
                    "-dMonoImageResolution=150",      # 单色图像较低分辨率
                    "-dColorImageDownsampleType=/Bicubic",
                    "-dGrayImageDownsampleType=/Bicubic",
                    "-dMonoImageDownsampleType=/Bicubic",
                    "-dAutoRotatePages=/None",
                    "-dDetectDuplicateImages=true",
                    "-dCompressFonts=true",
                    "-dSubsetFonts=true",
                    "-dEmbedAllFonts=false",          # 不嵌入字体
                    "-dOptimize=true",                # 优化PDF结构
                    "-dUseFlateCompression=true",     # 使用Flate压缩
                    "-dDownsampleColorImages=true",   # 下采样彩色图像
                    "-dDownsampleGrayImages=true",    # 下采样灰度图像
                    "-dColorImageFilter=/DCTEncode",  # JPEG压缩
                    "-dGrayImageFilter=/DCTEncode",   # JPEG压缩
                    "-dJPEGQ=30",                     # 低JPEG质量
                    f"-sOutputFile={output_path}",
                    str(input_path)
                ]
            elif quality == "screen":
                # 极限压缩设置
                cmd = [
                    gs_command,
                    "-sDEVICE=pdfwrite",
                    "-dCompatibilityLevel=1.4",
                    f"-dPDFSETTINGS=/{quality}",
                    "-dNOPAUSE",
                    "-dQUIET",
                    "-dBATCH",
                    "-dColorImageResolution=72",      # 更低分辨率
                    "-dGrayImageResolution=72",
                    "-dMonoImageResolution=300",      # 单色图像保持较高分辨率
                    "-dColorImageDownsampleType=/Bicubic",
                    "-dGrayImageDownsampleType=/Bicubic",
                    "-dMonoImageDownsampleType=/Bicubic",
                    "-dAutoRotatePages=/None",
                    "-dDetectDuplicateImages=true",
                    "-dCompressFonts=true",
                    "-dSubsetFonts=true",
                    "-dEmbedAllFonts=false",          # 不嵌入字体
                    "-dOptimize=true",                # 优化PDF结构
                    "-dUseFlateCompression=true",     # 使用Flate压缩
                    "-dColorConversionStrategy=/LeaveColorUnchanged",
                    "-dDownsampleColorImages=true",   # 下采样彩色图像
                    "-dDownsampleGrayImages=true",    # 下采样灰度图像
                    "-dColorImageFilter=/DCTEncode",  # JPEG压缩
                    "-dGrayImageFilter=/DCTEncode",   # JPEG压缩
                    f"-sOutputFile={output_path}",
                    str(input_path)
                ]
            else:
                # 标准压缩设置
                cmd = [
                    gs_command,
                    "-sDEVICE=pdfwrite",
                    "-dCompatibilityLevel=1.4",
                    f"-dPDFSETTINGS=/{quality}",
                    "-dNOPAUSE",
                    "-dQUIET",
                    "-dBATCH",
                    "-dColorImageResolution=150",
                    "-dGrayImageResolution=150",
                    "-dMonoImageResolution=150",
                    "-dColorImageDownsampleType=/Bicubic",
                    "-dGrayImageDownsampleType=/Bicubic",
                    "-dMonoImageDownsampleType=/Bicubic",
                    "-dAutoRotatePages=/None",
                    "-dDetectDuplicateImages=true",
                    "-dCompressFonts=true",
                    "-dSubsetFonts=true",
                    f"-sOutputFile={output_path}",
                    str(input_path)
                ]

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0 and output_path.exists():
                return True
            else:
                print(f"   ⚠️ Ghostscript错误: {result.stderr}")
                if result.stdout:
                    print(f"   📝 Ghostscript输出: {result.stdout}")
                return False

        except FileNotFoundError:
            print(f"   ❌ Ghostscript未找到，请确保已安装")
            return False
        except Exception as e:
            print(f"   ❌ Ghostscript压缩出错: {e}")
            return False
    
    def compress_pdf(self, input_path: Path, output_path: Path, 
                    compression_level: str = "medium") -> Tuple[bool, float, float]:
        """
        压缩PDF文件
        
        Args:
            input_path: 输入PDF路径
            output_path: 输出PDF路径
            compression_level: 压缩级别 ("low", "medium", "high", "extreme")
        
        Returns:
            Tuple[bool, float, float]: (成功状态, 原始大小MB, 压缩后大小MB)
        """
        if not input_path.exists():
            print(f"❌ 输入文件不存在: {input_path}")
            return False, 0, 0
        
        original_size = self.get_file_size_mb(input_path)
        print(f"📄 压缩文件: {input_path.name}")
        print(f"   原始大小: {original_size:.2f} MB")
        
        # 根据压缩级别选择Ghostscript质量设置
        gs_quality_map = {
            "low": "printer",
            "medium": "ebook",
            "high": "ebook",
            "extreme": "ultra_screen"  # 使用超级压缩模式
        }
        
        gs_quality = gs_quality_map.get(compression_level, "ebook")
        
        try:
            # 创建临时文件
            temp_file1 = Path(self.temp_dir) / "step1_pypdf2.pdf"
            temp_file2 = Path(self.temp_dir) / "step2_ghostscript.pdf"
            
            current_input = input_path
            ghostscript_success = False

            # 步骤1: PyPDF2基础压缩
            print("   🔧 步骤1: 基础压缩...")
            if self.compress_with_pypdf2(current_input, temp_file1):
                current_input = temp_file1
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")

            # 步骤2: Ghostscript高级压缩
            print("   🚀 步骤2: 高级压缩...")
            if self.compress_with_ghostscript(current_input, temp_file2, gs_quality):
                current_input = temp_file2
                current_size = self.get_file_size_mb(current_input)
                print(f"      当前大小: {current_size:.2f} MB")
                ghostscript_success = True
            else:
                print("      跳过Ghostscript压缩（未安装或失败）")
            
            # 复制最终结果
            shutil.copy2(current_input, output_path)
            final_size = self.get_file_size_mb(output_path)
            
            # 计算压缩比
            compression_ratio = final_size / original_size if original_size > 0 else 0
            space_saved = original_size - final_size
            
            print(f"   ✅ 压缩完成!")
            print(f"   📊 压缩统计:")
            print(f"      原始大小: {original_size:.2f} MB")
            print(f"      压缩后大小: {final_size:.2f} MB")
            print(f"      压缩比: {compression_ratio:.1%}")
            print(f"      节省空间: {space_saved:.2f} MB")
            
            # 检查是否达到目标
            if compression_ratio <= 0.25:
                print(f"      🎯 达到压缩目标（≤25%）")
            elif compression_ratio <= 0.35:
                print(f"      📈 接近压缩目标（≤35%）")
            elif ghostscript_success:
                print(f"      ✅ 压缩完成，Ghostscript已优化")
            else:
                print(f"      ⚠️ 建议安装Ghostscript以获得更好的压缩效果")
            
            return True, original_size, final_size
            
        except Exception as e:
            print(f"   ❌ 压缩失败: {e}")
            return False, original_size, 0
    
    def batch_compress(self, input_dir: Path, output_dir: Path = None, 
                      compression_level: str = "medium") -> dict:
        """批量压缩PDF文件"""
        if output_dir is None:
            output_dir = input_dir / "compressed"
        
        output_dir.mkdir(exist_ok=True)
        
        # 查找所有PDF文件
        pdf_files = list(input_dir.rglob("*.pdf"))
        
        if not pdf_files:
            print("❌ 没有找到PDF文件")
            return {}
        
        print(f"🔍 找到 {len(pdf_files)} 个PDF文件")
        print("=" * 60)
        
        stats = {
            "total_files": len(pdf_files),
            "successful": 0,
            "failed": 0,
            "total_original_size": 0,
            "total_compressed_size": 0
        }
        
        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"\n📚 处理文件 {i}/{len(pdf_files)}")
            
            # 生成输出文件路径
            relative_path = pdf_file.relative_to(input_dir)
            output_file = output_dir / relative_path
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 压缩文件
            success, original_size, compressed_size = self.compress_pdf(
                pdf_file, output_file, compression_level
            )
            
            if success:
                stats["successful"] += 1
                stats["total_original_size"] += original_size
                stats["total_compressed_size"] += compressed_size
            else:
                stats["failed"] += 1
        
        # 打印总体统计
        print("\n" + "=" * 60)
        print("📊 批量压缩统计:")
        print(f"   总文件数: {stats['total_files']}")
        print(f"   成功压缩: {stats['successful']}")
        print(f"   失败数量: {stats['failed']}")
        
        if stats["total_original_size"] > 0:
            total_ratio = stats["total_compressed_size"] / stats["total_original_size"]
            total_saved = stats["total_original_size"] - stats["total_compressed_size"]
            
            print(f"   原始总大小: {stats['total_original_size']:.2f} MB")
            print(f"   压缩后总大小: {stats['total_compressed_size']:.2f} MB")
            print(f"   总体压缩比: {total_ratio:.1%}")
            print(f"   总节省空间: {total_saved:.2f} MB")
        
        print(f"   输出目录: {output_dir}")
        
        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='简化版PDF压缩工具')
    parser.add_argument('input', help='输入PDF文件或目录')
    parser.add_argument('output', nargs='?', help='输出PDF文件或目录')
    parser.add_argument('--batch', '-b', action='store_true', help='批量处理模式')
    parser.add_argument('--level', '-l', choices=['low', 'medium', 'high', 'extreme'], 
                       default='medium', help='压缩级别（默认: medium）')
    
    args = parser.parse_args()
    
    input_path = Path(args.input)
    
    if not input_path.exists():
        print(f"❌ 输入路径不存在: {input_path}")
        sys.exit(1)
    
    print("🗜️ 简化版PDF压缩工具")
    print("=" * 50)
    print(f"📂 输入: {input_path}")
    print(f"⚙️ 压缩级别: {args.level}")
    print(f"🎯 目标: 压缩到原来的20%左右")
    
    # 检查Ghostscript
    try:
        gs_cmd = "gs" if os.name != 'nt' else "gswin64c"
        result = subprocess.run([gs_cmd, "--version"], capture_output=True)
        if result.returncode == 0:
            print("✅ Ghostscript已安装，将获得最佳压缩效果")
        else:
            print("⚠️ Ghostscript未安装，压缩效果可能有限")
    except:
        print("⚠️ Ghostscript未安装，压缩效果可能有限")
        print("💡 建议安装Ghostscript以获得更好的压缩效果")
    
    print()
    
    try:
        with SimplePDFCompressor() as compressor:
            if args.batch or input_path.is_dir():
                # 批量处理模式
                output_dir = Path(args.output) if args.output else None
                stats = compressor.batch_compress(input_path, output_dir, args.level)
                
            else:
                # 单文件处理模式
                if not args.output:
                    output_path = input_path.parent / f"{input_path.stem}_compressed.pdf"
                else:
                    output_path = Path(args.output)
                
                print(f"📄 输出: {output_path}")
                print()
                
                success, original_size, compressed_size = compressor.compress_pdf(
                    input_path, output_path, args.level
                )
                
                if not success:
                    sys.exit(1)
        
        print("\n✅ 压缩完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
